<template>
  <div>
    <!-- 顶部Banner -->
    <div class="banner-container"
      style="position: relative; background-image: url('/images/pp1.jpg');background-repeat: no-repeat; background-size:auto; background-position: center;">
      <div class="container" style="position: relative; ">
        <div style="position: absolute;left: 60%;top:200px">
          <h2 style="color: #222; font-size: 3rem; font-weight: bold;font-style: italic; margin-bottom: 12px;">方草捆集垛机</h2>
          <p style="color: #222; font-size: 1.4rem;">操作方便&#160;&#160;&#160;&#160;  经济高效</p>
          <div class="mt-5 font-light">
            提升带强压功能，机具入土轻松，作业效率高，配置2组液压输出，满足多样化机具作业；
          </div>
          <div style="margin-top: 24px;">
            <button class="btn" style="background: #f14d4d; color: #fff; margin-right: 16px;">获取报价</button>
            <!-- <button class="btn" style="background: #222; color: #fff;">产品对比</button> -->
          </div>
        </div>
        <div style="position: absolute;left: 5%;top:120px">
          <img src="/images/pp1_3.png" alt="产品图片" style="width: 600px; height: auto;mix-blend-mode: darken;"></img>
        </div>
      </div>
    </div>


    <div style="background-color: #fff;">
      <!-- 产品详情内容（全部用图片占位） -->
      <section class="container" style="padding: 48px;">
        <a name="js"></a>
        <div class="flex-around" style="margin-bottom: 32px;">
          <img src="/images/pp1_1.png" alt="产品特点"
            style=" object-fit: cover; border-radius: 8px; box-shadow: 0 2px 8px #0001;">
        </div>

        <a name="td"></a>
        <div class="section-title" style="">
          产品特点
        </div>

        <div>

          <img src="/images/pp11.jpg" alt="产品特点" style=" object-fit: cover; border-radius: 8px; ">
        </div>

        <a name="cs"></a>
        <div class="section-title" style="">
          产品参数
        </div>

        <div class="flex-around" style="margin-bottom: 32px;">
          <img src="/images/pp1_4.png" alt="产品参数"
            style=" object-fit: cover; border-radius: 8px; box-shadow: 0 2px 8px #0001;">
        </div>

      </section>


      <!-- 相关产品 -->

      <section style="background: #f6f6f6; padding: 48px 0;">
        <div class="container">
          <h3 class="section-title" style="margin-bottom: 32px;">相关产品 ({{ allProducts.length }}个)</h3>
          <!-- 相关产品滑动组件 -->
          <div class="related-products-slider">
            <div class="slider-container">
              <!-- 左箭头 -->
              <button
                class="slider-arrow slider-arrow-left"
                @click="slideLeft"
                :disabled="currentIndex === 0"
              >
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </button>

              <!-- 产品列表 -->
              <div class="slider-wrapper" ref="sliderWrapper">
                <div
                  class="slider-track"
                  :style="{ transform: `translateX(-${currentIndex * slideWidth}px)` }"
                >
                  <div
                    v-for="(product, index) in allProducts"
                    :key="index"
                    class="product-item"
                    @click="onProductClick(product)"
                  >
                    <div class="product-image">
                      <img
                        :src="product.img"
                        :alt="product.name"
                        @error="handleImageError"
                      />
                    </div>
                    <div class="product-name">{{ product.name }}</div>
                  </div>
                </div>
              </div>

              <!-- 右箭头 -->
              <button
                class="slider-arrow slider-arrow-right"
                @click="slideRight"
                :disabled="currentIndex >= maxIndex"
              >
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import productsData from '../../assets/products'

function scrollToAnchor(name: string) {
  document.querySelector("a[name=" + name + "]")?.scrollIntoView({ behavior: "smooth" })
}

// 相关产品滑动逻辑
const currentIndex = ref(0)
const slideWidth = ref(300) // 每个产品项的宽度
const sliderWrapper = ref<HTMLElement>()

// 获取所有产品（扁平化处理）
const allProducts = computed(() => {
  const products: any[] = []
  productsData.forEach((category: any) => {
    if (category.children) {
      products.push(...category.children)
    }
  })
  console.log('加载的产品数据:', products)
  return products
})

// 计算每次显示的产品数量
const itemsPerView = computed(() => {
  const windowWidth = window.innerWidth
  if (windowWidth <= 768) return 1
  if (windowWidth <= 1024) return 2
  return 3
})

// 计算最大索引
const maxIndex = computed(() => {
  return Math.max(0, allProducts.value.length - itemsPerView.value)
})

// 向左滑动
const slideLeft = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--
  }
}

// 向右滑动
const slideRight = () => {
  if (currentIndex.value < maxIndex.value) {
    currentIndex.value++
  }
}

// 组件挂载后计算实际宽度
onMounted(async () => {
  await nextTick()
  updateSlideWidth()

  // 监听窗口大小变化
  window.addEventListener('resize', updateSlideWidth)
})

// 组件卸载时清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', updateSlideWidth)
})

// 更新滑动宽度
const updateSlideWidth = () => {
  if (sliderWrapper.value) {
    const containerWidth = sliderWrapper.value.offsetWidth
    const windowWidth = window.innerWidth

    // 根据屏幕宽度决定显示的产品数量
    let itemsPerView = 3
    if (windowWidth <= 768) {
      itemsPerView = 1
    } else if (windowWidth <= 1024) {
      itemsPerView = 2
    }

    slideWidth.value = containerWidth / itemsPerView
  }
}

// 产品点击事件
const onProductClick = (product: any) => {
  console.log('点击产品:', product)
  // 这里可以添加跳转到产品详情页的逻辑
  // 例如: router.push(`/product/detail/${product.id}`)
}

// 图片加载错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/placeholder.jpg' // 设置默认图片
}
</script>

<style scoped>
.container {
  background-color: #fff;

}

.banner-container {
  height: 900px;

}

.tab-item {
  color: #fff;
  padding: 0 32px;
  height: 48px;
  line-height: 48px;
  font-size: 1.1rem;
  cursor: pointer;
  position: relative;
}

.tab-item.active {
  background: #fff;
  color: var(--primary-color);
  border-radius: 4px 4px 0 0;
}

.section-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 32px 0 16px 0;
  line-height: 1.2;
  color: #222;
  letter-spacing: 1px;
}

/* 相关产品滑动组件样式 */
.related-products-slider {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.slider-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: 20px;
}

.slider-arrow {
  background: #fff;
  border: 2px solid #e0e0e0;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 2;
  flex-shrink: 0;
}

.slider-arrow:hover:not(:disabled) {
  background: #f5f5f5;
  border-color: #ccc;
  transform: scale(1.05);
}

.slider-arrow:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.slider-arrow svg {
  color: #666;
}

.slider-wrapper {
  flex: 1;
  overflow: hidden;
  border-radius: 8px;
}

.slider-track {
  display: flex;
  transition: transform 0.5s ease;
  gap: 20px;
}

.product-item {
  flex: 0 0 calc(33.333% - 14px);
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.product-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.product-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  background: #f8f8f8;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-item:hover .product-image img {
  transform: scale(1.05);
}

.product-name {
  padding: 16px;
  font-size: 1rem;
  font-weight: 500;
  color: #333;
  text-align: center;
  line-height: 1.4;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .product-item {
    flex: 0 0 calc(50% - 10px);
  }
}

@media (max-width: 768px) {
  .slider-container {
    gap: 10px;
  }

  .slider-arrow {
    width: 40px;
    height: 40px;
  }

  .product-item {
    flex: 0 0 calc(100% - 20px);
  }

  .product-image {
    height: 150px;
  }

  .product-name {
    padding: 12px;
    font-size: 0.9rem;
    min-height: 50px;
  }
}

@media (max-width: 480px) {
  .slider-container {
    gap: 5px;
  }

  .slider-arrow {
    width: 36px;
    height: 36px;
  }

  .product-item {
    flex: 0 0 100%;
  }

  .product-image {
    height: 120px;
  }
}
</style>