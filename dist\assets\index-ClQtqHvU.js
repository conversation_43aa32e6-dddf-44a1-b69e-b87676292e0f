(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))s(i);new MutationObserver(i=>{for(const r of i)if(r.type==="childList")for(const l of r.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&s(l)}).observe(document,{childList:!0,subtree:!0});function n(i){const r={};return i.integrity&&(r.integrity=i.integrity),i.referrerPolicy&&(r.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?r.credentials="include":i.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function s(i){if(i.ep)return;i.ep=!0;const r=n(i);fetch(i.href,r)}})();/**
* @vue/shared v3.5.17
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Gs(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const ge={},Ft=[],it=()=>{},Ul=()=>!1,qn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Ws=e=>e.startsWith("onUpdate:"),Me=Object.assign,qs=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Yl=Object.prototype.hasOwnProperty,oe=(e,t)=>Yl.call(e,t),Z=Array.isArray,Nt=e=>Un(e)==="[object Map]",vr=e=>Un(e)==="[object Set]",te=e=>typeof e=="function",we=e=>typeof e=="string",gt=e=>typeof e=="symbol",ye=e=>e!==null&&typeof e=="object",yr=e=>(ye(e)||te(e))&&te(e.then)&&te(e.catch),br=Object.prototype.toString,Un=e=>br.call(e),Xl=e=>Un(e).slice(8,-1),wr=e=>Un(e)==="[object Object]",Us=e=>we(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,ln=Gs(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Yn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Kl=/-(\w)/g,qe=Yn(e=>e.replace(Kl,(t,n)=>n?n.toUpperCase():"")),Ql=/\B([A-Z])/g,Rt=Yn(e=>e.replace(Ql,"-$1").toLowerCase()),Xn=Yn(e=>e.charAt(0).toUpperCase()+e.slice(1)),rs=Yn(e=>e?`on${Xn(e)}`:""),Et=(e,t)=>!Object.is(e,t),ls=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Ps=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},Jl=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let wi;const Kn=()=>wi||(wi=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Ys(e){if(Z(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],i=we(s)?no(s):Ys(s);if(i)for(const r in i)t[r]=i[r]}return t}else if(we(e)||ye(e))return e}const Zl=/;(?![^(]*\))/g,eo=/:([^]+)/,to=/\/\*[^]*?\*\//g;function no(e){const t={};return e.replace(to,"").split(Zl).forEach(n=>{if(n){const s=n.split(eo);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Tt(e){let t="";if(we(e))t=e;else if(Z(e))for(let n=0;n<e.length;n++){const s=Tt(e[n]);s&&(t+=s+" ")}else if(ye(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const so="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",io=Gs(so);function Sr(e){return!!e||e===""}const xr=e=>!!(e&&e.__v_isRef===!0),be=e=>we(e)?e:e==null?"":Z(e)||ye(e)&&(e.toString===br||!te(e.toString))?xr(e)?be(e.value):JSON.stringify(e,_r,2):String(e),_r=(e,t)=>xr(t)?_r(e,t.value):Nt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,i],r)=>(n[os(s,r)+" =>"]=i,n),{})}:vr(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>os(n))}:gt(t)?os(t):ye(t)&&!Z(t)&&!wr(t)?String(t):t,os=(e,t="")=>{var n;return gt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ze;class ro{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=ze,!t&&ze&&(this.index=(ze.scopes||(ze.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=ze;try{return ze=this,t()}finally{ze=n}}}on(){++this._on===1&&(this.prevScope=ze,ze=this)}off(){this._on>0&&--this._on===0&&(ze=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const i=this.parent.scopes.pop();i&&i!==this&&(this.parent.scopes[this.index]=i,i.index=this.index)}this.parent=void 0}}}function lo(){return ze}let me;const as=new WeakSet;class Er{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,ze&&ze.active&&ze.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,as.has(this)&&(as.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Cr(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Si(this),Pr(this);const t=me,n=Ye;me=this,Ye=!0;try{return this.fn()}finally{Mr(this),me=t,Ye=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Qs(t);this.deps=this.depsTail=void 0,Si(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?as.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Ms(this)&&this.run()}get dirty(){return Ms(this)}}let Tr=0,on,an;function Cr(e,t=!1){if(e.flags|=8,t){e.next=an,an=e;return}e.next=on,on=e}function Xs(){Tr++}function Ks(){if(--Tr>0)return;if(an){let t=an;for(an=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;on;){let t=on;for(on=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Pr(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Mr(e){let t,n=e.depsTail,s=n;for(;s;){const i=s.prevDep;s.version===-1?(s===n&&(n=i),Qs(s),oo(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=i}e.deps=t,e.depsTail=n}function Ms(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Or(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Or(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===hn)||(e.globalVersion=hn,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Ms(e))))return;e.flags|=2;const t=e.dep,n=me,s=Ye;me=e,Ye=!0;try{Pr(e);const i=e.fn(e._value);(t.version===0||Et(i,e._value))&&(e.flags|=128,e._value=i,t.version++)}catch(i){throw t.version++,i}finally{me=n,Ye=s,Mr(e),e.flags&=-3}}function Qs(e,t=!1){const{dep:n,prevSub:s,nextSub:i}=e;if(s&&(s.nextSub=i,e.prevSub=void 0),i&&(i.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let r=n.computed.deps;r;r=r.nextDep)Qs(r,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function oo(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Ye=!0;const Ir=[];function pt(){Ir.push(Ye),Ye=!1}function ht(){const e=Ir.pop();Ye=e===void 0?!0:e}function Si(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=me;me=void 0;try{t()}finally{me=n}}}let hn=0;class ao{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Js{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!me||!Ye||me===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==me)n=this.activeLink=new ao(me,this),me.deps?(n.prevDep=me.depsTail,me.depsTail.nextDep=n,me.depsTail=n):me.deps=me.depsTail=n,Ar(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=me.depsTail,n.nextDep=void 0,me.depsTail.nextDep=n,me.depsTail=n,me.deps===n&&(me.deps=s)}return n}trigger(t){this.version++,hn++,this.notify(t)}notify(t){Xs();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Ks()}}}function Ar(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)Ar(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Os=new WeakMap,At=Symbol(""),Is=Symbol(""),mn=Symbol("");function Te(e,t,n){if(Ye&&me){let s=Os.get(e);s||Os.set(e,s=new Map);let i=s.get(n);i||(s.set(n,i=new Js),i.map=s,i.key=n),i.track()}}function ft(e,t,n,s,i,r){const l=Os.get(e);if(!l){hn++;return}const o=a=>{a&&a.trigger()};if(Xs(),t==="clear")l.forEach(o);else{const a=Z(e),d=a&&Us(n);if(a&&n==="length"){const c=Number(s);l.forEach((u,f)=>{(f==="length"||f===mn||!gt(f)&&f>=c)&&o(u)})}else switch((n!==void 0||l.has(void 0))&&o(l.get(n)),d&&o(l.get(mn)),t){case"add":a?d&&o(l.get("length")):(o(l.get(At)),Nt(e)&&o(l.get(Is)));break;case"delete":a||(o(l.get(At)),Nt(e)&&o(l.get(Is)));break;case"set":Nt(e)&&o(l.get(At));break}}Ks()}function Bt(e){const t=le(e);return t===e?t:(Te(t,"iterate",mn),We(e)?t:t.map(Ee))}function Qn(e){return Te(e=le(e),"iterate",mn),e}const co={__proto__:null,[Symbol.iterator](){return cs(this,Symbol.iterator,Ee)},concat(...e){return Bt(this).concat(...e.map(t=>Z(t)?Bt(t):t))},entries(){return cs(this,"entries",e=>(e[1]=Ee(e[1]),e))},every(e,t){return at(this,"every",e,t,void 0,arguments)},filter(e,t){return at(this,"filter",e,t,n=>n.map(Ee),arguments)},find(e,t){return at(this,"find",e,t,Ee,arguments)},findIndex(e,t){return at(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return at(this,"findLast",e,t,Ee,arguments)},findLastIndex(e,t){return at(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return at(this,"forEach",e,t,void 0,arguments)},includes(...e){return ds(this,"includes",e)},indexOf(...e){return ds(this,"indexOf",e)},join(e){return Bt(this).join(e)},lastIndexOf(...e){return ds(this,"lastIndexOf",e)},map(e,t){return at(this,"map",e,t,void 0,arguments)},pop(){return en(this,"pop")},push(...e){return en(this,"push",e)},reduce(e,...t){return xi(this,"reduce",e,t)},reduceRight(e,...t){return xi(this,"reduceRight",e,t)},shift(){return en(this,"shift")},some(e,t){return at(this,"some",e,t,void 0,arguments)},splice(...e){return en(this,"splice",e)},toReversed(){return Bt(this).toReversed()},toSorted(e){return Bt(this).toSorted(e)},toSpliced(...e){return Bt(this).toSpliced(...e)},unshift(...e){return en(this,"unshift",e)},values(){return cs(this,"values",Ee)}};function cs(e,t,n){const s=Qn(e),i=s[t]();return s!==e&&!We(e)&&(i._next=i.next,i.next=()=>{const r=i._next();return r.value&&(r.value=n(r.value)),r}),i}const uo=Array.prototype;function at(e,t,n,s,i,r){const l=Qn(e),o=l!==e&&!We(e),a=l[t];if(a!==uo[t]){const u=a.apply(e,r);return o?Ee(u):u}let d=n;l!==e&&(o?d=function(u,f){return n.call(this,Ee(u),f,e)}:n.length>2&&(d=function(u,f){return n.call(this,u,f,e)}));const c=a.call(l,d,s);return o&&i?i(c):c}function xi(e,t,n,s){const i=Qn(e);let r=n;return i!==e&&(We(e)?n.length>3&&(r=function(l,o,a){return n.call(this,l,o,a,e)}):r=function(l,o,a){return n.call(this,l,Ee(o),a,e)}),i[t](r,...s)}function ds(e,t,n){const s=le(e);Te(s,"iterate",mn);const i=s[t](...n);return(i===-1||i===!1)&&ti(n[0])?(n[0]=le(n[0]),s[t](...n)):i}function en(e,t,n=[]){pt(),Xs();const s=le(e)[t].apply(e,n);return Ks(),ht(),s}const fo=Gs("__proto__,__v_isRef,__isVue"),Lr=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(gt));function po(e){gt(e)||(e=String(e));const t=le(this);return Te(t,"has",e),t.hasOwnProperty(e)}class Rr{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const i=this._isReadonly,r=this._isShallow;if(n==="__v_isReactive")return!i;if(n==="__v_isReadonly")return i;if(n==="__v_isShallow")return r;if(n==="__v_raw")return s===(i?r?_o:kr:r?Br:zr).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const l=Z(t);if(!i){let a;if(l&&(a=co[n]))return a;if(n==="hasOwnProperty")return po}const o=Reflect.get(t,n,Pe(t)?t:s);return(gt(n)?Lr.has(n):fo(n))||(i||Te(t,"get",n),r)?o:Pe(o)?l&&Us(n)?o:o.value:ye(o)?i?Fr(o):Jn(o):o}}class $r extends Rr{constructor(t=!1){super(!1,t)}set(t,n,s,i){let r=t[n];if(!this._isShallow){const a=Ct(r);if(!We(s)&&!Ct(s)&&(r=le(r),s=le(s)),!Z(t)&&Pe(r)&&!Pe(s))return a?!1:(r.value=s,!0)}const l=Z(t)&&Us(n)?Number(n)<t.length:oe(t,n),o=Reflect.set(t,n,s,Pe(t)?t:i);return t===le(i)&&(l?Et(s,r)&&ft(t,"set",n,s):ft(t,"add",n,s)),o}deleteProperty(t,n){const s=oe(t,n);t[n];const i=Reflect.deleteProperty(t,n);return i&&s&&ft(t,"delete",n,void 0),i}has(t,n){const s=Reflect.has(t,n);return(!gt(n)||!Lr.has(n))&&Te(t,"has",n),s}ownKeys(t){return Te(t,"iterate",Z(t)?"length":At),Reflect.ownKeys(t)}}class ho extends Rr{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const mo=new $r,go=new ho,vo=new $r(!0);const As=e=>e,Tn=e=>Reflect.getPrototypeOf(e);function yo(e,t,n){return function(...s){const i=this.__v_raw,r=le(i),l=Nt(r),o=e==="entries"||e===Symbol.iterator&&l,a=e==="keys"&&l,d=i[e](...s),c=n?As:t?$n:Ee;return!t&&Te(r,"iterate",a?Is:At),{next(){const{value:u,done:f}=d.next();return f?{value:u,done:f}:{value:o?[c(u[0]),c(u[1])]:c(u),done:f}},[Symbol.iterator](){return this}}}}function Cn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function bo(e,t){const n={get(i){const r=this.__v_raw,l=le(r),o=le(i);e||(Et(i,o)&&Te(l,"get",i),Te(l,"get",o));const{has:a}=Tn(l),d=t?As:e?$n:Ee;if(a.call(l,i))return d(r.get(i));if(a.call(l,o))return d(r.get(o));r!==l&&r.get(i)},get size(){const i=this.__v_raw;return!e&&Te(le(i),"iterate",At),Reflect.get(i,"size",i)},has(i){const r=this.__v_raw,l=le(r),o=le(i);return e||(Et(i,o)&&Te(l,"has",i),Te(l,"has",o)),i===o?r.has(i):r.has(i)||r.has(o)},forEach(i,r){const l=this,o=l.__v_raw,a=le(o),d=t?As:e?$n:Ee;return!e&&Te(a,"iterate",At),o.forEach((c,u)=>i.call(r,d(c),d(u),l))}};return Me(n,e?{add:Cn("add"),set:Cn("set"),delete:Cn("delete"),clear:Cn("clear")}:{add(i){!t&&!We(i)&&!Ct(i)&&(i=le(i));const r=le(this);return Tn(r).has.call(r,i)||(r.add(i),ft(r,"add",i,i)),this},set(i,r){!t&&!We(r)&&!Ct(r)&&(r=le(r));const l=le(this),{has:o,get:a}=Tn(l);let d=o.call(l,i);d||(i=le(i),d=o.call(l,i));const c=a.call(l,i);return l.set(i,r),d?Et(r,c)&&ft(l,"set",i,r):ft(l,"add",i,r),this},delete(i){const r=le(this),{has:l,get:o}=Tn(r);let a=l.call(r,i);a||(i=le(i),a=l.call(r,i)),o&&o.call(r,i);const d=r.delete(i);return a&&ft(r,"delete",i,void 0),d},clear(){const i=le(this),r=i.size!==0,l=i.clear();return r&&ft(i,"clear",void 0,void 0),l}}),["keys","values","entries",Symbol.iterator].forEach(i=>{n[i]=yo(i,e,t)}),n}function Zs(e,t){const n=bo(e,t);return(s,i,r)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?s:Reflect.get(oe(n,i)&&i in s?n:s,i,r)}const wo={get:Zs(!1,!1)},So={get:Zs(!1,!0)},xo={get:Zs(!0,!1)};const zr=new WeakMap,Br=new WeakMap,kr=new WeakMap,_o=new WeakMap;function Eo(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function To(e){return e.__v_skip||!Object.isExtensible(e)?0:Eo(Xl(e))}function Jn(e){return Ct(e)?e:ei(e,!1,mo,wo,zr)}function Dr(e){return ei(e,!1,vo,So,Br)}function Fr(e){return ei(e,!0,go,xo,kr)}function ei(e,t,n,s,i){if(!ye(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const r=To(e);if(r===0)return e;const l=i.get(e);if(l)return l;const o=new Proxy(e,r===2?s:n);return i.set(e,o),o}function jt(e){return Ct(e)?jt(e.__v_raw):!!(e&&e.__v_isReactive)}function Ct(e){return!!(e&&e.__v_isReadonly)}function We(e){return!!(e&&e.__v_isShallow)}function ti(e){return e?!!e.__v_raw:!1}function le(e){const t=e&&e.__v_raw;return t?le(t):e}function Co(e){return!oe(e,"__v_skip")&&Object.isExtensible(e)&&Ps(e,"__v_skip",!0),e}const Ee=e=>ye(e)?Jn(e):e,$n=e=>ye(e)?Fr(e):e;function Pe(e){return e?e.__v_isRef===!0:!1}function pe(e){return Nr(e,!1)}function Po(e){return Nr(e,!0)}function Nr(e,t){return Pe(e)?e:new Mo(e,t)}class Mo{constructor(t,n){this.dep=new Js,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:le(t),this._value=n?t:Ee(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||We(t)||Ct(t);t=s?t:le(t),Et(t,n)&&(this._rawValue=t,this._value=s?t:Ee(t),this.dep.trigger())}}function nt(e){return Pe(e)?e.value:e}const Oo={get:(e,t,n)=>t==="__v_raw"?e:nt(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const i=e[t];return Pe(i)&&!Pe(n)?(i.value=n,!0):Reflect.set(e,t,n,s)}};function jr(e){return jt(e)?e:new Proxy(e,Oo)}class Io{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Js(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=hn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&me!==this)return Cr(this,!0),!0}get value(){const t=this.dep.track();return Or(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Ao(e,t,n=!1){let s,i;return te(e)?s=e:(s=e.get,i=e.set),new Io(s,i,n)}const Pn={},zn=new WeakMap;let It;function Lo(e,t=!1,n=It){if(n){let s=zn.get(n);s||zn.set(n,s=[]),s.push(e)}}function Ro(e,t,n=ge){const{immediate:s,deep:i,once:r,scheduler:l,augmentJob:o,call:a}=n,d=x=>i?x:We(x)||i===!1||i===0?xt(x,1):xt(x);let c,u,f,m,g=!1,S=!1;if(Pe(e)?(u=()=>e.value,g=We(e)):jt(e)?(u=()=>d(e),g=!0):Z(e)?(S=!0,g=e.some(x=>jt(x)||We(x)),u=()=>e.map(x=>{if(Pe(x))return x.value;if(jt(x))return d(x);if(te(x))return a?a(x,2):x()})):te(e)?t?u=a?()=>a(e,2):e:u=()=>{if(f){pt();try{f()}finally{ht()}}const x=It;It=c;try{return a?a(e,3,[m]):e(m)}finally{It=x}}:u=it,t&&i){const x=u,E=i===!0?1/0:i;u=()=>xt(x(),E)}const C=lo(),b=()=>{c.stop(),C&&C.active&&qs(C.effects,c)};if(r&&t){const x=t;t=(...E)=>{x(...E),b()}}let h=S?new Array(e.length).fill(Pn):Pn;const y=x=>{if(!(!(c.flags&1)||!c.dirty&&!x))if(t){const E=c.run();if(i||g||(S?E.some((k,H)=>Et(k,h[H])):Et(E,h))){f&&f();const k=It;It=c;try{const H=[E,h===Pn?void 0:S&&h[0]===Pn?[]:h,m];h=E,a?a(t,3,H):t(...H)}finally{It=k}}}else c.run()};return o&&o(y),c=new Er(u),c.scheduler=l?()=>l(y,!1):y,m=x=>Lo(x,!1,c),f=c.onStop=()=>{const x=zn.get(c);if(x){if(a)a(x,4);else for(const E of x)E();zn.delete(c)}},t?s?y(!0):h=c.run():l?l(y.bind(null,!0),!0):c.run(),b.pause=c.pause.bind(c),b.resume=c.resume.bind(c),b.stop=b,b}function xt(e,t=1/0,n){if(t<=0||!ye(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Pe(e))xt(e.value,t,n);else if(Z(e))for(let s=0;s<e.length;s++)xt(e[s],t,n);else if(vr(e)||Nt(e))e.forEach(s=>{xt(s,t,n)});else if(wr(e)){for(const s in e)xt(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&xt(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Sn(e,t,n,s){try{return s?e(...s):e()}catch(i){Zn(i,t,n)}}function ot(e,t,n,s){if(te(e)){const i=Sn(e,t,n,s);return i&&yr(i)&&i.catch(r=>{Zn(r,t,n)}),i}if(Z(e)){const i=[];for(let r=0;r<e.length;r++)i.push(ot(e[r],t,n,s));return i}}function Zn(e,t,n,s=!0){const i=t?t.vnode:null,{errorHandler:r,throwUnhandledErrorInProduction:l}=t&&t.appContext.config||ge;if(t){let o=t.parent;const a=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${n}`;for(;o;){const c=o.ec;if(c){for(let u=0;u<c.length;u++)if(c[u](e,a,d)===!1)return}o=o.parent}if(r){pt(),Sn(r,null,10,[e,a,d]),ht();return}}$o(e,n,i,s,l)}function $o(e,t,n,s=!0,i=!1){if(i)throw e;console.error(e)}const Ie=[];let et=-1;const Ht=[];let bt=null,kt=0;const Hr=Promise.resolve();let Bn=null;function ni(e){const t=Bn||Hr;return e?t.then(this?e.bind(this):e):t}function zo(e){let t=et+1,n=Ie.length;for(;t<n;){const s=t+n>>>1,i=Ie[s],r=gn(i);r<e||r===e&&i.flags&2?t=s+1:n=s}return t}function si(e){if(!(e.flags&1)){const t=gn(e),n=Ie[Ie.length-1];!n||!(e.flags&2)&&t>=gn(n)?Ie.push(e):Ie.splice(zo(t),0,e),e.flags|=1,Vr()}}function Vr(){Bn||(Bn=Hr.then(Wr))}function Bo(e){Z(e)?Ht.push(...e):bt&&e.id===-1?bt.splice(kt+1,0,e):e.flags&1||(Ht.push(e),e.flags|=1),Vr()}function _i(e,t,n=et+1){for(;n<Ie.length;n++){const s=Ie[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;Ie.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function Gr(e){if(Ht.length){const t=[...new Set(Ht)].sort((n,s)=>gn(n)-gn(s));if(Ht.length=0,bt){bt.push(...t);return}for(bt=t,kt=0;kt<bt.length;kt++){const n=bt[kt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}bt=null,kt=0}}const gn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Wr(e){try{for(et=0;et<Ie.length;et++){const t=Ie[et];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Sn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;et<Ie.length;et++){const t=Ie[et];t&&(t.flags&=-2)}et=-1,Ie.length=0,Gr(),Bn=null,(Ie.length||Ht.length)&&Wr()}}let Ae=null,qr=null;function kn(e){const t=Ae;return Ae=e,qr=e&&e.type.__scopeId||null,t}function He(e,t=Ae,n){if(!t||e._n)return e;const s=(...i)=>{s._d&&Ri(-1);const r=kn(t);let l;try{l=e(...i)}finally{kn(r),s._d&&Ri(1)}return l};return s._n=!0,s._c=!0,s._d=!0,s}function Mt(e,t,n,s){const i=e.dirs,r=t&&t.dirs;for(let l=0;l<i.length;l++){const o=i[l];r&&(o.oldValue=r[l].value);let a=o.dir[s];a&&(pt(),ot(a,n,8,[e.el,o,e,t]),ht())}}const ko=Symbol("_vte"),Do=e=>e.__isTeleport;function ii(e,t){e.shapeFlag&6&&e.component?(e.transition=t,ii(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Pt(e,t){return te(e)?Me({name:e.name},t,{setup:e}):e}function Ur(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function cn(e,t,n,s,i=!1){if(Z(e)){e.forEach((g,S)=>cn(g,t&&(Z(t)?t[S]:t),n,s,i));return}if(Vt(s)&&!i){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&cn(e,t,n,s.component.subTree);return}const r=s.shapeFlag&4?ui(s.component):s.el,l=i?null:r,{i:o,r:a}=e,d=t&&t.r,c=o.refs===ge?o.refs={}:o.refs,u=o.setupState,f=le(u),m=u===ge?()=>!1:g=>oe(f,g);if(d!=null&&d!==a&&(we(d)?(c[d]=null,m(d)&&(u[d]=null)):Pe(d)&&(d.value=null)),te(a))Sn(a,o,12,[l,c]);else{const g=we(a),S=Pe(a);if(g||S){const C=()=>{if(e.f){const b=g?m(a)?u[a]:c[a]:a.value;i?Z(b)&&qs(b,r):Z(b)?b.includes(r)||b.push(r):g?(c[a]=[r],m(a)&&(u[a]=c[a])):(a.value=[r],e.k&&(c[e.k]=a.value))}else g?(c[a]=l,m(a)&&(u[a]=l)):S&&(a.value=l,e.k&&(c[e.k]=l))};l?(C.id=-1,Fe(C,n)):C()}}}Kn().requestIdleCallback;Kn().cancelIdleCallback;const Vt=e=>!!e.type.__asyncLoader,Yr=e=>e.type.__isKeepAlive;function Fo(e,t){Xr(e,"a",t)}function No(e,t){Xr(e,"da",t)}function Xr(e,t,n=Ce){const s=e.__wdc||(e.__wdc=()=>{let i=n;for(;i;){if(i.isDeactivated)return;i=i.parent}return e()});if(es(t,s,n),n){let i=n.parent;for(;i&&i.parent;)Yr(i.parent.vnode)&&jo(s,t,n,i),i=i.parent}}function jo(e,t,n,s){const i=es(t,e,s,!0);Qr(()=>{qs(s[t],i)},n)}function es(e,t,n=Ce,s=!1){if(n){const i=n[e]||(n[e]=[]),r=t.__weh||(t.__weh=(...l)=>{pt();const o=_n(n),a=ot(t,n,e,l);return o(),ht(),a});return s?i.unshift(r):i.push(r),r}}const vt=e=>(t,n=Ce)=>{(!bn||e==="sp")&&es(e,(...s)=>t(...s),n)},Ho=vt("bm"),ri=vt("m"),Kr=vt("bu"),li=vt("u"),oi=vt("bum"),Qr=vt("um"),Vo=vt("sp"),Go=vt("rtg"),Wo=vt("rtc");function qo(e,t=Ce){es("ec",e,t)}const Uo="components";function xn(e,t){return Xo(Uo,e,!0,t)||e}const Yo=Symbol.for("v-ndc");function Xo(e,t,n=!0,s=!1){const i=Ae||Ce;if(i){const r=i.type;{const o=Ba(r,!1);if(o&&(o===t||o===qe(t)||o===Xn(qe(t))))return r}const l=Ei(i[e]||r[e],t)||Ei(i.appContext[e],t);return!l&&s?r:l}}function Ei(e,t){return e&&(e[t]||e[qe(t)]||e[Xn(qe(t))])}function ke(e,t,n,s){let i;const r=n,l=Z(e);if(l||we(e)){const o=l&&jt(e);let a=!1,d=!1;o&&(a=!We(e),d=Ct(e),e=Qn(e)),i=new Array(e.length);for(let c=0,u=e.length;c<u;c++)i[c]=t(a?d?$n(Ee(e[c])):Ee(e[c]):e[c],c,void 0,r)}else if(typeof e=="number"){i=new Array(e);for(let o=0;o<e;o++)i[o]=t(o+1,o,void 0,r)}else if(ye(e))if(e[Symbol.iterator])i=Array.from(e,(o,a)=>t(o,a,void 0,r));else{const o=Object.keys(e);i=new Array(o.length);for(let a=0,d=o.length;a<d;a++){const c=o[a];i[a]=t(e[c],c,a,r)}}else i=[];return i}function Ko(e,t,n={},s,i){if(Ae.ce||Ae.parent&&Vt(Ae.parent)&&Ae.parent.ce)return K(),Xt(ue,null,[re("slot",n,s)],64);let r=e[t];r&&r._c&&(r._d=!1),K();const l=r&&Jr(r(n)),o=n.key||l&&l.key,a=Xt(ue,{key:(o&&!gt(o)?o:`_${t}`)+(!l&&s?"_fb":"")},l||[],l&&e._===1?64:-2);return r&&r._c&&(r._d=!0),a}function Jr(e){return e.some(t=>yn(t)?!(t.type===mt||t.type===ue&&!Jr(t.children)):!0)?e:null}const Ls=e=>e?yl(e)?ui(e):Ls(e.parent):null,dn=Me(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ls(e.parent),$root:e=>Ls(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>el(e),$forceUpdate:e=>e.f||(e.f=()=>{si(e.update)}),$nextTick:e=>e.n||(e.n=ni.bind(e.proxy)),$watch:e=>va.bind(e)}),us=(e,t)=>e!==ge&&!e.__isScriptSetup&&oe(e,t),Qo={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:i,props:r,accessCache:l,type:o,appContext:a}=e;let d;if(t[0]!=="$"){const m=l[t];if(m!==void 0)switch(m){case 1:return s[t];case 2:return i[t];case 4:return n[t];case 3:return r[t]}else{if(us(s,t))return l[t]=1,s[t];if(i!==ge&&oe(i,t))return l[t]=2,i[t];if((d=e.propsOptions[0])&&oe(d,t))return l[t]=3,r[t];if(n!==ge&&oe(n,t))return l[t]=4,n[t];Rs&&(l[t]=0)}}const c=dn[t];let u,f;if(c)return t==="$attrs"&&Te(e.attrs,"get",""),c(e);if((u=o.__cssModules)&&(u=u[t]))return u;if(n!==ge&&oe(n,t))return l[t]=4,n[t];if(f=a.config.globalProperties,oe(f,t))return f[t]},set({_:e},t,n){const{data:s,setupState:i,ctx:r}=e;return us(i,t)?(i[t]=n,!0):s!==ge&&oe(s,t)?(s[t]=n,!0):oe(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(r[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:i,propsOptions:r}},l){let o;return!!n[l]||e!==ge&&oe(e,l)||us(t,l)||(o=r[0])&&oe(o,l)||oe(s,l)||oe(dn,l)||oe(i.config.globalProperties,l)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:oe(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Ti(e){return Z(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Rs=!0;function Jo(e){const t=el(e),n=e.proxy,s=e.ctx;Rs=!1,t.beforeCreate&&Ci(t.beforeCreate,e,"bc");const{data:i,computed:r,methods:l,watch:o,provide:a,inject:d,created:c,beforeMount:u,mounted:f,beforeUpdate:m,updated:g,activated:S,deactivated:C,beforeDestroy:b,beforeUnmount:h,destroyed:y,unmounted:x,render:E,renderTracked:k,renderTriggered:H,errorCaptured:z,serverPrefetch:O,expose:A,inheritAttrs:B,components:Y,directives:X,filters:se}=t;if(d&&Zo(d,s,null),l)for(const Q in l){const D=l[Q];te(D)&&(s[Q]=D.bind(n))}if(i){const Q=i.call(n,n);ye(Q)&&(e.data=Jn(Q))}if(Rs=!0,r)for(const Q in r){const D=r[Q],ae=te(D)?D.bind(n,n):te(D.get)?D.get.bind(n,n):it,ve=!te(D)&&te(D.set)?D.set.bind(n):it,xe=je({get:ae,set:ve});Object.defineProperty(s,Q,{enumerable:!0,configurable:!0,get:()=>xe.value,set:Se=>xe.value=Se})}if(o)for(const Q in o)Zr(o[Q],s,n,Q);if(a){const Q=te(a)?a.call(n):a;Reflect.ownKeys(Q).forEach(D=>{Wt(D,Q[D])})}c&&Ci(c,e,"c");function G(Q,D){Z(D)?D.forEach(ae=>Q(ae.bind(n))):D&&Q(D.bind(n))}if(G(Ho,u),G(ri,f),G(Kr,m),G(li,g),G(Fo,S),G(No,C),G(qo,z),G(Wo,k),G(Go,H),G(oi,h),G(Qr,x),G(Vo,O),Z(A))if(A.length){const Q=e.exposed||(e.exposed={});A.forEach(D=>{Object.defineProperty(Q,D,{get:()=>n[D],set:ae=>n[D]=ae})})}else e.exposed||(e.exposed={});E&&e.render===it&&(e.render=E),B!=null&&(e.inheritAttrs=B),Y&&(e.components=Y),X&&(e.directives=X),O&&Ur(e)}function Zo(e,t,n=it){Z(e)&&(e=$s(e));for(const s in e){const i=e[s];let r;ye(i)?"default"in i?r=rt(i.from||s,i.default,!0):r=rt(i.from||s):r=rt(i),Pe(r)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>r.value,set:l=>r.value=l}):t[s]=r}}function Ci(e,t,n){ot(Z(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Zr(e,t,n,s){let i=s.includes(".")?pl(n,s):()=>n[s];if(we(e)){const r=t[e];te(r)&&qt(i,r)}else if(te(e))qt(i,e.bind(n));else if(ye(e))if(Z(e))e.forEach(r=>Zr(r,t,n,s));else{const r=te(e.handler)?e.handler.bind(n):t[e.handler];te(r)&&qt(i,r,e)}}function el(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:i,optionsCache:r,config:{optionMergeStrategies:l}}=e.appContext,o=r.get(t);let a;return o?a=o:!i.length&&!n&&!s?a=t:(a={},i.length&&i.forEach(d=>Dn(a,d,l,!0)),Dn(a,t,l)),ye(t)&&r.set(t,a),a}function Dn(e,t,n,s=!1){const{mixins:i,extends:r}=t;r&&Dn(e,r,n,!0),i&&i.forEach(l=>Dn(e,l,n,!0));for(const l in t)if(!(s&&l==="expose")){const o=ea[l]||n&&n[l];e[l]=o?o(e[l],t[l]):t[l]}return e}const ea={data:Pi,props:Mi,emits:Mi,methods:rn,computed:rn,beforeCreate:Oe,created:Oe,beforeMount:Oe,mounted:Oe,beforeUpdate:Oe,updated:Oe,beforeDestroy:Oe,beforeUnmount:Oe,destroyed:Oe,unmounted:Oe,activated:Oe,deactivated:Oe,errorCaptured:Oe,serverPrefetch:Oe,components:rn,directives:rn,watch:na,provide:Pi,inject:ta};function Pi(e,t){return t?e?function(){return Me(te(e)?e.call(this,this):e,te(t)?t.call(this,this):t)}:t:e}function ta(e,t){return rn($s(e),$s(t))}function $s(e){if(Z(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Oe(e,t){return e?[...new Set([].concat(e,t))]:t}function rn(e,t){return e?Me(Object.create(null),e,t):t}function Mi(e,t){return e?Z(e)&&Z(t)?[...new Set([...e,...t])]:Me(Object.create(null),Ti(e),Ti(t??{})):t}function na(e,t){if(!e)return t;if(!t)return e;const n=Me(Object.create(null),e);for(const s in t)n[s]=Oe(e[s],t[s]);return n}function tl(){return{app:null,config:{isNativeTag:Ul,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let sa=0;function ia(e,t){return function(s,i=null){te(s)||(s=Me({},s)),i!=null&&!ye(i)&&(i=null);const r=tl(),l=new WeakSet,o=[];let a=!1;const d=r.app={_uid:sa++,_component:s,_props:i,_container:null,_context:r,_instance:null,version:Da,get config(){return r.config},set config(c){},use(c,...u){return l.has(c)||(c&&te(c.install)?(l.add(c),c.install(d,...u)):te(c)&&(l.add(c),c(d,...u))),d},mixin(c){return r.mixins.includes(c)||r.mixins.push(c),d},component(c,u){return u?(r.components[c]=u,d):r.components[c]},directive(c,u){return u?(r.directives[c]=u,d):r.directives[c]},mount(c,u,f){if(!a){const m=d._ceVNode||re(s,i);return m.appContext=r,f===!0?f="svg":f===!1&&(f=void 0),e(m,c,f),a=!0,d._container=c,c.__vue_app__=d,ui(m.component)}},onUnmount(c){o.push(c)},unmount(){a&&(ot(o,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(c,u){return r.provides[c]=u,d},runWithContext(c){const u=Gt;Gt=d;try{return c()}finally{Gt=u}}};return d}}let Gt=null;function Wt(e,t){if(Ce){let n=Ce.provides;const s=Ce.parent&&Ce.parent.provides;s===n&&(n=Ce.provides=Object.create(s)),n[e]=t}}function rt(e,t,n=!1){const s=Ce||Ae;if(s||Gt){let i=Gt?Gt._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(i&&e in i)return i[e];if(arguments.length>1)return n&&te(t)?t.call(s&&s.proxy):t}}const nl={},sl=()=>Object.create(nl),il=e=>Object.getPrototypeOf(e)===nl;function ra(e,t,n,s=!1){const i={},r=sl();e.propsDefaults=Object.create(null),rl(e,t,i,r);for(const l in e.propsOptions[0])l in i||(i[l]=void 0);n?e.props=s?i:Dr(i):e.type.props?e.props=i:e.props=r,e.attrs=r}function la(e,t,n,s){const{props:i,attrs:r,vnode:{patchFlag:l}}=e,o=le(i),[a]=e.propsOptions;let d=!1;if((s||l>0)&&!(l&16)){if(l&8){const c=e.vnode.dynamicProps;for(let u=0;u<c.length;u++){let f=c[u];if(ts(e.emitsOptions,f))continue;const m=t[f];if(a)if(oe(r,f))m!==r[f]&&(r[f]=m,d=!0);else{const g=qe(f);i[g]=zs(a,o,g,m,e,!1)}else m!==r[f]&&(r[f]=m,d=!0)}}}else{rl(e,t,i,r)&&(d=!0);let c;for(const u in o)(!t||!oe(t,u)&&((c=Rt(u))===u||!oe(t,c)))&&(a?n&&(n[u]!==void 0||n[c]!==void 0)&&(i[u]=zs(a,o,u,void 0,e,!0)):delete i[u]);if(r!==o)for(const u in r)(!t||!oe(t,u))&&(delete r[u],d=!0)}d&&ft(e.attrs,"set","")}function rl(e,t,n,s){const[i,r]=e.propsOptions;let l=!1,o;if(t)for(let a in t){if(ln(a))continue;const d=t[a];let c;i&&oe(i,c=qe(a))?!r||!r.includes(c)?n[c]=d:(o||(o={}))[c]=d:ts(e.emitsOptions,a)||(!(a in s)||d!==s[a])&&(s[a]=d,l=!0)}if(r){const a=le(n),d=o||ge;for(let c=0;c<r.length;c++){const u=r[c];n[u]=zs(i,a,u,d[u],e,!oe(d,u))}}return l}function zs(e,t,n,s,i,r){const l=e[n];if(l!=null){const o=oe(l,"default");if(o&&s===void 0){const a=l.default;if(l.type!==Function&&!l.skipFactory&&te(a)){const{propsDefaults:d}=i;if(n in d)s=d[n];else{const c=_n(i);s=d[n]=a.call(null,t),c()}}else s=a;i.ce&&i.ce._setProp(n,s)}l[0]&&(r&&!o?s=!1:l[1]&&(s===""||s===Rt(n))&&(s=!0))}return s}const oa=new WeakMap;function ll(e,t,n=!1){const s=n?oa:t.propsCache,i=s.get(e);if(i)return i;const r=e.props,l={},o=[];let a=!1;if(!te(e)){const c=u=>{a=!0;const[f,m]=ll(u,t,!0);Me(l,f),m&&o.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!r&&!a)return ye(e)&&s.set(e,Ft),Ft;if(Z(r))for(let c=0;c<r.length;c++){const u=qe(r[c]);Oi(u)&&(l[u]=ge)}else if(r)for(const c in r){const u=qe(c);if(Oi(u)){const f=r[c],m=l[u]=Z(f)||te(f)?{type:f}:Me({},f),g=m.type;let S=!1,C=!0;if(Z(g))for(let b=0;b<g.length;++b){const h=g[b],y=te(h)&&h.name;if(y==="Boolean"){S=!0;break}else y==="String"&&(C=!1)}else S=te(g)&&g.name==="Boolean";m[0]=S,m[1]=C,(S||oe(m,"default"))&&o.push(u)}}const d=[l,o];return ye(e)&&s.set(e,d),d}function Oi(e){return e[0]!=="$"&&!ln(e)}const ai=e=>e[0]==="_"||e==="$stable",ci=e=>Z(e)?e.map(tt):[tt(e)],aa=(e,t,n)=>{if(t._n)return t;const s=He((...i)=>ci(t(...i)),n);return s._c=!1,s},ol=(e,t,n)=>{const s=e._ctx;for(const i in e){if(ai(i))continue;const r=e[i];if(te(r))t[i]=aa(i,r,s);else if(r!=null){const l=ci(r);t[i]=()=>l}}},al=(e,t)=>{const n=ci(t);e.slots.default=()=>n},cl=(e,t,n)=>{for(const s in t)(n||!ai(s))&&(e[s]=t[s])},ca=(e,t,n)=>{const s=e.slots=sl();if(e.vnode.shapeFlag&32){const i=t.__;i&&Ps(s,"__",i,!0);const r=t._;r?(cl(s,t,n),n&&Ps(s,"_",r,!0)):ol(t,s)}else t&&al(e,t)},da=(e,t,n)=>{const{vnode:s,slots:i}=e;let r=!0,l=ge;if(s.shapeFlag&32){const o=t._;o?n&&o===1?r=!1:cl(i,t,n):(r=!t.$stable,ol(t,i)),l=t}else t&&(al(e,t),l={default:1});if(r)for(const o in i)!ai(o)&&l[o]==null&&delete i[o]},Fe=Ea;function ua(e){return fa(e)}function fa(e,t){const n=Kn();n.__VUE__=!0;const{insert:s,remove:i,patchProp:r,createElement:l,createText:o,createComment:a,setText:d,setElementText:c,parentNode:u,nextSibling:f,setScopeId:m=it,insertStaticContent:g}=e,S=(p,v,w,T=null,I=null,M=null,F=void 0,$=null,R=!!v.dynamicChildren)=>{if(p===v)return;p&&!tn(p,v)&&(T=P(p),Se(p,I,M,!0),p=null),v.patchFlag===-2&&(R=!1,v.dynamicChildren=null);const{type:L,ref:U,shapeFlag:j}=v;switch(L){case ns:C(p,v,w,T);break;case mt:b(p,v,w,T);break;case In:p==null&&h(v,w,T,F);break;case ue:Y(p,v,w,T,I,M,F,$,R);break;default:j&1?E(p,v,w,T,I,M,F,$,R):j&6?X(p,v,w,T,I,M,F,$,R):(j&64||j&128)&&L.process(p,v,w,T,I,M,F,$,R,W)}U!=null&&I?cn(U,p&&p.ref,M,v||p,!v):U==null&&p&&p.ref!=null&&cn(p.ref,null,M,p,!0)},C=(p,v,w,T)=>{if(p==null)s(v.el=o(v.children),w,T);else{const I=v.el=p.el;v.children!==p.children&&d(I,v.children)}},b=(p,v,w,T)=>{p==null?s(v.el=a(v.children||""),w,T):v.el=p.el},h=(p,v,w,T)=>{[p.el,p.anchor]=g(p.children,v,w,T,p.el,p.anchor)},y=({el:p,anchor:v},w,T)=>{let I;for(;p&&p!==v;)I=f(p),s(p,w,T),p=I;s(v,w,T)},x=({el:p,anchor:v})=>{let w;for(;p&&p!==v;)w=f(p),i(p),p=w;i(v)},E=(p,v,w,T,I,M,F,$,R)=>{v.type==="svg"?F="svg":v.type==="math"&&(F="mathml"),p==null?k(v,w,T,I,M,F,$,R):O(p,v,I,M,F,$,R)},k=(p,v,w,T,I,M,F,$)=>{let R,L;const{props:U,shapeFlag:j,transition:q,dirs:J}=p;if(R=p.el=l(p.type,M,U&&U.is,U),j&8?c(R,p.children):j&16&&z(p.children,R,null,T,I,fs(p,M),F,$),J&&Mt(p,null,T,"created"),H(R,p,p.scopeId,F,T),U){for(const he in U)he!=="value"&&!ln(he)&&r(R,he,null,U[he],M,T);"value"in U&&r(R,"value",null,U.value,M),(L=U.onVnodeBeforeMount)&&Ze(L,T,p)}J&&Mt(p,null,T,"beforeMount");const ne=pa(I,q);ne&&q.beforeEnter(R),s(R,v,w),((L=U&&U.onVnodeMounted)||ne||J)&&Fe(()=>{L&&Ze(L,T,p),ne&&q.enter(R),J&&Mt(p,null,T,"mounted")},I)},H=(p,v,w,T,I)=>{if(w&&m(p,w),T)for(let M=0;M<T.length;M++)m(p,T[M]);if(I){let M=I.subTree;if(v===M||ml(M.type)&&(M.ssContent===v||M.ssFallback===v)){const F=I.vnode;H(p,F,F.scopeId,F.slotScopeIds,I.parent)}}},z=(p,v,w,T,I,M,F,$,R=0)=>{for(let L=R;L<p.length;L++){const U=p[L]=$?wt(p[L]):tt(p[L]);S(null,U,v,w,T,I,M,F,$)}},O=(p,v,w,T,I,M,F)=>{const $=v.el=p.el;let{patchFlag:R,dynamicChildren:L,dirs:U}=v;R|=p.patchFlag&16;const j=p.props||ge,q=v.props||ge;let J;if(w&&Ot(w,!1),(J=q.onVnodeBeforeUpdate)&&Ze(J,w,v,p),U&&Mt(v,p,w,"beforeUpdate"),w&&Ot(w,!0),(j.innerHTML&&q.innerHTML==null||j.textContent&&q.textContent==null)&&c($,""),L?A(p.dynamicChildren,L,$,w,T,fs(v,I),M):F||D(p,v,$,null,w,T,fs(v,I),M,!1),R>0){if(R&16)B($,j,q,w,I);else if(R&2&&j.class!==q.class&&r($,"class",null,q.class,I),R&4&&r($,"style",j.style,q.style,I),R&8){const ne=v.dynamicProps;for(let he=0;he<ne.length;he++){const ce=ne[he],Re=j[ce],$e=q[ce];($e!==Re||ce==="value")&&r($,ce,Re,$e,I,w)}}R&1&&p.children!==v.children&&c($,v.children)}else!F&&L==null&&B($,j,q,w,I);((J=q.onVnodeUpdated)||U)&&Fe(()=>{J&&Ze(J,w,v,p),U&&Mt(v,p,w,"updated")},T)},A=(p,v,w,T,I,M,F)=>{for(let $=0;$<v.length;$++){const R=p[$],L=v[$],U=R.el&&(R.type===ue||!tn(R,L)||R.shapeFlag&198)?u(R.el):w;S(R,L,U,null,T,I,M,F,!0)}},B=(p,v,w,T,I)=>{if(v!==w){if(v!==ge)for(const M in v)!ln(M)&&!(M in w)&&r(p,M,v[M],null,I,T);for(const M in w){if(ln(M))continue;const F=w[M],$=v[M];F!==$&&M!=="value"&&r(p,M,$,F,I,T)}"value"in w&&r(p,"value",v.value,w.value,I)}},Y=(p,v,w,T,I,M,F,$,R)=>{const L=v.el=p?p.el:o(""),U=v.anchor=p?p.anchor:o("");let{patchFlag:j,dynamicChildren:q,slotScopeIds:J}=v;J&&($=$?$.concat(J):J),p==null?(s(L,w,T),s(U,w,T),z(v.children||[],w,U,I,M,F,$,R)):j>0&&j&64&&q&&p.dynamicChildren?(A(p.dynamicChildren,q,w,I,M,F,$),(v.key!=null||I&&v===I.subTree)&&dl(p,v,!0)):D(p,v,w,U,I,M,F,$,R)},X=(p,v,w,T,I,M,F,$,R)=>{v.slotScopeIds=$,p==null?v.shapeFlag&512?I.ctx.activate(v,w,T,F,R):se(v,w,T,I,M,F,R):ie(p,v,R)},se=(p,v,w,T,I,M,F)=>{const $=p.component=Aa(p,T,I);if(Yr(p)&&($.ctx.renderer=W),La($,!1,F),$.asyncDep){if(I&&I.registerDep($,G,F),!p.el){const R=$.subTree=re(mt);b(null,R,v,w)}}else G($,p,v,w,I,M,F)},ie=(p,v,w)=>{const T=v.component=p.component;if(xa(p,v,w))if(T.asyncDep&&!T.asyncResolved){Q(T,v,w);return}else T.next=v,T.update();else v.el=p.el,T.vnode=v},G=(p,v,w,T,I,M,F)=>{const $=()=>{if(p.isMounted){let{next:j,bu:q,u:J,parent:ne,vnode:he}=p;{const Qe=ul(p);if(Qe){j&&(j.el=he.el,Q(p,j,F)),Qe.asyncDep.then(()=>{p.isUnmounted||$()});return}}let ce=j,Re;Ot(p,!1),j?(j.el=he.el,Q(p,j,F)):j=he,q&&ls(q),(Re=j.props&&j.props.onVnodeBeforeUpdate)&&Ze(Re,ne,j,he),Ot(p,!0);const $e=Ai(p),Ke=p.subTree;p.subTree=$e,S(Ke,$e,u(Ke.el),P(Ke),p,I,M),j.el=$e.el,ce===null&&_a(p,$e.el),J&&Fe(J,I),(Re=j.props&&j.props.onVnodeUpdated)&&Fe(()=>Ze(Re,ne,j,he),I)}else{let j;const{el:q,props:J}=v,{bm:ne,m:he,parent:ce,root:Re,type:$e}=p,Ke=Vt(v);Ot(p,!1),ne&&ls(ne),!Ke&&(j=J&&J.onVnodeBeforeMount)&&Ze(j,ce,v),Ot(p,!0);{Re.ce&&Re.ce._def.shadowRoot!==!1&&Re.ce._injectChildStyle($e);const Qe=p.subTree=Ai(p);S(null,Qe,w,T,p,I,M),v.el=Qe.el}if(he&&Fe(he,I),!Ke&&(j=J&&J.onVnodeMounted)){const Qe=v;Fe(()=>Ze(j,ce,Qe),I)}(v.shapeFlag&256||ce&&Vt(ce.vnode)&&ce.vnode.shapeFlag&256)&&p.a&&Fe(p.a,I),p.isMounted=!0,v=w=T=null}};p.scope.on();const R=p.effect=new Er($);p.scope.off();const L=p.update=R.run.bind(R),U=p.job=R.runIfDirty.bind(R);U.i=p,U.id=p.uid,R.scheduler=()=>si(U),Ot(p,!0),L()},Q=(p,v,w)=>{v.component=p;const T=p.vnode.props;p.vnode=v,p.next=null,la(p,v.props,T,w),da(p,v.children,w),pt(),_i(p),ht()},D=(p,v,w,T,I,M,F,$,R=!1)=>{const L=p&&p.children,U=p?p.shapeFlag:0,j=v.children,{patchFlag:q,shapeFlag:J}=v;if(q>0){if(q&128){ve(L,j,w,T,I,M,F,$,R);return}else if(q&256){ae(L,j,w,T,I,M,F,$,R);return}}J&8?(U&16&&Ge(L,I,M),j!==L&&c(w,j)):U&16?J&16?ve(L,j,w,T,I,M,F,$,R):Ge(L,I,M,!0):(U&8&&c(w,""),J&16&&z(j,w,T,I,M,F,$,R))},ae=(p,v,w,T,I,M,F,$,R)=>{p=p||Ft,v=v||Ft;const L=p.length,U=v.length,j=Math.min(L,U);let q;for(q=0;q<j;q++){const J=v[q]=R?wt(v[q]):tt(v[q]);S(p[q],J,w,null,I,M,F,$,R)}L>U?Ge(p,I,M,!0,!1,j):z(v,w,T,I,M,F,$,R,j)},ve=(p,v,w,T,I,M,F,$,R)=>{let L=0;const U=v.length;let j=p.length-1,q=U-1;for(;L<=j&&L<=q;){const J=p[L],ne=v[L]=R?wt(v[L]):tt(v[L]);if(tn(J,ne))S(J,ne,w,null,I,M,F,$,R);else break;L++}for(;L<=j&&L<=q;){const J=p[j],ne=v[q]=R?wt(v[q]):tt(v[q]);if(tn(J,ne))S(J,ne,w,null,I,M,F,$,R);else break;j--,q--}if(L>j){if(L<=q){const J=q+1,ne=J<U?v[J].el:T;for(;L<=q;)S(null,v[L]=R?wt(v[L]):tt(v[L]),w,ne,I,M,F,$,R),L++}}else if(L>q)for(;L<=j;)Se(p[L],I,M,!0),L++;else{const J=L,ne=L,he=new Map;for(L=ne;L<=q;L++){const De=v[L]=R?wt(v[L]):tt(v[L]);De.key!=null&&he.set(De.key,L)}let ce,Re=0;const $e=q-ne+1;let Ke=!1,Qe=0;const Zt=new Array($e);for(L=0;L<$e;L++)Zt[L]=0;for(L=J;L<=j;L++){const De=p[L];if(Re>=$e){Se(De,I,M,!0);continue}let Je;if(De.key!=null)Je=he.get(De.key);else for(ce=ne;ce<=q;ce++)if(Zt[ce-ne]===0&&tn(De,v[ce])){Je=ce;break}Je===void 0?Se(De,I,M,!0):(Zt[Je-ne]=L+1,Je>=Qe?Qe=Je:Ke=!0,S(De,v[Je],w,null,I,M,F,$,R),Re++)}const yi=Ke?ha(Zt):Ft;for(ce=yi.length-1,L=$e-1;L>=0;L--){const De=ne+L,Je=v[De],bi=De+1<U?v[De+1].el:T;Zt[L]===0?S(null,Je,w,bi,I,M,F,$,R):Ke&&(ce<0||L!==yi[ce]?xe(Je,w,bi,2):ce--)}}},xe=(p,v,w,T,I=null)=>{const{el:M,type:F,transition:$,children:R,shapeFlag:L}=p;if(L&6){xe(p.component.subTree,v,w,T);return}if(L&128){p.suspense.move(v,w,T);return}if(L&64){F.move(p,v,w,W);return}if(F===ue){s(M,v,w);for(let j=0;j<R.length;j++)xe(R[j],v,w,T);s(p.anchor,v,w);return}if(F===In){y(p,v,w);return}if(T!==2&&L&1&&$)if(T===0)$.beforeEnter(M),s(M,v,w),Fe(()=>$.enter(M),I);else{const{leave:j,delayLeave:q,afterLeave:J}=$,ne=()=>{p.ctx.isUnmounted?i(M):s(M,v,w)},he=()=>{j(M,()=>{ne(),J&&J()})};q?q(M,ne,he):he()}else s(M,v,w)},Se=(p,v,w,T=!1,I=!1)=>{const{type:M,props:F,ref:$,children:R,dynamicChildren:L,shapeFlag:U,patchFlag:j,dirs:q,cacheIndex:J}=p;if(j===-2&&(I=!1),$!=null&&(pt(),cn($,null,w,p,!0),ht()),J!=null&&(v.renderCache[J]=void 0),U&256){v.ctx.deactivate(p);return}const ne=U&1&&q,he=!Vt(p);let ce;if(he&&(ce=F&&F.onVnodeBeforeUnmount)&&Ze(ce,v,p),U&6)En(p.component,w,T);else{if(U&128){p.suspense.unmount(w,T);return}ne&&Mt(p,null,v,"beforeUnmount"),U&64?p.type.remove(p,v,w,W,T):L&&!L.hasOnce&&(M!==ue||j>0&&j&64)?Ge(L,v,w,!1,!0):(M===ue&&j&384||!I&&U&16)&&Ge(R,v,w),T&&$t(p)}(he&&(ce=F&&F.onVnodeUnmounted)||ne)&&Fe(()=>{ce&&Ze(ce,v,p),ne&&Mt(p,null,v,"unmounted")},w)},$t=p=>{const{type:v,el:w,anchor:T,transition:I}=p;if(v===ue){zt(w,T);return}if(v===In){x(p);return}const M=()=>{i(w),I&&!I.persisted&&I.afterLeave&&I.afterLeave()};if(p.shapeFlag&1&&I&&!I.persisted){const{leave:F,delayLeave:$}=I,R=()=>F(w,M);$?$(p.el,M,R):R()}else M()},zt=(p,v)=>{let w;for(;p!==v;)w=f(p),i(p),p=w;i(v)},En=(p,v,w)=>{const{bum:T,scope:I,job:M,subTree:F,um:$,m:R,a:L,parent:U,slots:{__:j}}=p;Ii(R),Ii(L),T&&ls(T),U&&Z(j)&&j.forEach(q=>{U.renderCache[q]=void 0}),I.stop(),M&&(M.flags|=8,Se(F,p,v,w)),$&&Fe($,v),Fe(()=>{p.isUnmounted=!0},v),v&&v.pendingBranch&&!v.isUnmounted&&p.asyncDep&&!p.asyncResolved&&p.suspenseId===v.pendingId&&(v.deps--,v.deps===0&&v.resolve())},Ge=(p,v,w,T=!1,I=!1,M=0)=>{for(let F=M;F<p.length;F++)Se(p[F],v,w,T,I)},P=p=>{if(p.shapeFlag&6)return P(p.component.subTree);if(p.shapeFlag&128)return p.suspense.next();const v=f(p.anchor||p.el),w=v&&v[ko];return w?f(w):v};let V=!1;const N=(p,v,w)=>{p==null?v._vnode&&Se(v._vnode,null,null,!0):S(v._vnode||null,p,v,null,null,null,w),v._vnode=p,V||(V=!0,_i(),Gr(),V=!1)},W={p:S,um:Se,m:xe,r:$t,mt:se,mc:z,pc:D,pbc:A,n:P,o:e};return{render:N,hydrate:void 0,createApp:ia(N)}}function fs({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Ot({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function pa(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function dl(e,t,n=!1){const s=e.children,i=t.children;if(Z(s)&&Z(i))for(let r=0;r<s.length;r++){const l=s[r];let o=i[r];o.shapeFlag&1&&!o.dynamicChildren&&((o.patchFlag<=0||o.patchFlag===32)&&(o=i[r]=wt(i[r]),o.el=l.el),!n&&o.patchFlag!==-2&&dl(l,o)),o.type===ns&&(o.el=l.el),o.type===mt&&!o.el&&(o.el=l.el)}}function ha(e){const t=e.slice(),n=[0];let s,i,r,l,o;const a=e.length;for(s=0;s<a;s++){const d=e[s];if(d!==0){if(i=n[n.length-1],e[i]<d){t[s]=i,n.push(s);continue}for(r=0,l=n.length-1;r<l;)o=r+l>>1,e[n[o]]<d?r=o+1:l=o;d<e[n[r]]&&(r>0&&(t[s]=n[r-1]),n[r]=s)}}for(r=n.length,l=n[r-1];r-- >0;)n[r]=l,l=t[l];return n}function ul(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:ul(t)}function Ii(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const ma=Symbol.for("v-scx"),ga=()=>rt(ma);function qt(e,t,n){return fl(e,t,n)}function fl(e,t,n=ge){const{immediate:s,deep:i,flush:r,once:l}=n,o=Me({},n),a=t&&s||!t&&r!=="post";let d;if(bn){if(r==="sync"){const m=ga();d=m.__watcherHandles||(m.__watcherHandles=[])}else if(!a){const m=()=>{};return m.stop=it,m.resume=it,m.pause=it,m}}const c=Ce;o.call=(m,g,S)=>ot(m,c,g,S);let u=!1;r==="post"?o.scheduler=m=>{Fe(m,c&&c.suspense)}:r!=="sync"&&(u=!0,o.scheduler=(m,g)=>{g?m():si(m)}),o.augmentJob=m=>{t&&(m.flags|=4),u&&(m.flags|=2,c&&(m.id=c.uid,m.i=c))};const f=Ro(e,t,o);return bn&&(d?d.push(f):a&&f()),f}function va(e,t,n){const s=this.proxy,i=we(e)?e.includes(".")?pl(s,e):()=>s[e]:e.bind(s,s);let r;te(t)?r=t:(r=t.handler,n=t);const l=_n(this),o=fl(i,r.bind(s),n);return l(),o}function pl(e,t){const n=t.split(".");return()=>{let s=e;for(let i=0;i<n.length&&s;i++)s=s[n[i]];return s}}const ya=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${qe(t)}Modifiers`]||e[`${Rt(t)}Modifiers`];function ba(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||ge;let i=n;const r=t.startsWith("update:"),l=r&&ya(s,t.slice(7));l&&(l.trim&&(i=n.map(c=>we(c)?c.trim():c)),l.number&&(i=n.map(Jl)));let o,a=s[o=rs(t)]||s[o=rs(qe(t))];!a&&r&&(a=s[o=rs(Rt(t))]),a&&ot(a,e,6,i);const d=s[o+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[o])return;e.emitted[o]=!0,ot(d,e,6,i)}}function hl(e,t,n=!1){const s=t.emitsCache,i=s.get(e);if(i!==void 0)return i;const r=e.emits;let l={},o=!1;if(!te(e)){const a=d=>{const c=hl(d,t,!0);c&&(o=!0,Me(l,c))};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!r&&!o?(ye(e)&&s.set(e,null),null):(Z(r)?r.forEach(a=>l[a]=null):Me(l,r),ye(e)&&s.set(e,l),l)}function ts(e,t){return!e||!qn(t)?!1:(t=t.slice(2).replace(/Once$/,""),oe(e,t[0].toLowerCase()+t.slice(1))||oe(e,Rt(t))||oe(e,t))}function Ai(e){const{type:t,vnode:n,proxy:s,withProxy:i,propsOptions:[r],slots:l,attrs:o,emit:a,render:d,renderCache:c,props:u,data:f,setupState:m,ctx:g,inheritAttrs:S}=e,C=kn(e);let b,h;try{if(n.shapeFlag&4){const x=i||s,E=x;b=tt(d.call(E,x,c,u,m,f,g)),h=o}else{const x=t;b=tt(x.length>1?x(u,{attrs:o,slots:l,emit:a}):x(u,null)),h=t.props?o:wa(o)}}catch(x){un.length=0,Zn(x,e,1),b=re(mt)}let y=b;if(h&&S!==!1){const x=Object.keys(h),{shapeFlag:E}=y;x.length&&E&7&&(r&&x.some(Ws)&&(h=Sa(h,r)),y=Kt(y,h,!1,!0))}return n.dirs&&(y=Kt(y,null,!1,!0),y.dirs=y.dirs?y.dirs.concat(n.dirs):n.dirs),n.transition&&ii(y,n.transition),b=y,kn(C),b}const wa=e=>{let t;for(const n in e)(n==="class"||n==="style"||qn(n))&&((t||(t={}))[n]=e[n]);return t},Sa=(e,t)=>{const n={};for(const s in e)(!Ws(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function xa(e,t,n){const{props:s,children:i,component:r}=e,{props:l,children:o,patchFlag:a}=t,d=r.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&a>=0){if(a&1024)return!0;if(a&16)return s?Li(s,l,d):!!l;if(a&8){const c=t.dynamicProps;for(let u=0;u<c.length;u++){const f=c[u];if(l[f]!==s[f]&&!ts(d,f))return!0}}}else return(i||o)&&(!o||!o.$stable)?!0:s===l?!1:s?l?Li(s,l,d):!0:!!l;return!1}function Li(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let i=0;i<s.length;i++){const r=s[i];if(t[r]!==e[r]&&!ts(n,r))return!0}return!1}function _a({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const ml=e=>e.__isSuspense;function Ea(e,t){t&&t.pendingBranch?Z(e)?t.effects.push(...e):t.effects.push(e):Bo(e)}const ue=Symbol.for("v-fgt"),ns=Symbol.for("v-txt"),mt=Symbol.for("v-cmt"),In=Symbol.for("v-stc"),un=[];let Ve=null;function K(e=!1){un.push(Ve=e?null:[])}function Ta(){un.pop(),Ve=un[un.length-1]||null}let vn=1;function Ri(e,t=!1){vn+=e,e<0&&Ve&&t&&(Ve.hasOnce=!0)}function gl(e){return e.dynamicChildren=vn>0?Ve||Ft:null,Ta(),vn>0&&Ve&&Ve.push(e),e}function ee(e,t,n,s,i,r){return gl(_(e,t,n,s,i,r,!0))}function Xt(e,t,n,s,i){return gl(re(e,t,n,s,i,!0))}function yn(e){return e?e.__v_isVNode===!0:!1}function tn(e,t){return e.type===t.type&&e.key===t.key}const vl=({key:e})=>e??null,An=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?we(e)||Pe(e)||te(e)?{i:Ae,r:e,k:t,f:!!n}:e:null);function _(e,t=null,n=null,s=0,i=null,r=e===ue?0:1,l=!1,o=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&vl(t),ref:t&&An(t),scopeId:qr,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:s,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:Ae};return o?(di(a,n),r&128&&e.normalize(a)):n&&(a.shapeFlag|=we(n)?8:16),vn>0&&!l&&Ve&&(a.patchFlag>0||r&6)&&a.patchFlag!==32&&Ve.push(a),a}const re=Ca;function Ca(e,t=null,n=null,s=0,i=null,r=!1){if((!e||e===Yo)&&(e=mt),yn(e)){const o=Kt(e,t,!0);return n&&di(o,n),vn>0&&!r&&Ve&&(o.shapeFlag&6?Ve[Ve.indexOf(e)]=o:Ve.push(o)),o.patchFlag=-2,o}if(ka(e)&&(e=e.__vccOpts),t){t=Pa(t);let{class:o,style:a}=t;o&&!we(o)&&(t.class=Tt(o)),ye(a)&&(ti(a)&&!Z(a)&&(a=Me({},a)),t.style=Ys(a))}const l=we(e)?1:ml(e)?128:Do(e)?64:ye(e)?4:te(e)?2:0;return _(e,t,n,s,i,l,r,!0)}function Pa(e){return e?ti(e)||il(e)?Me({},e):e:null}function Kt(e,t,n=!1,s=!1){const{props:i,ref:r,patchFlag:l,children:o,transition:a}=e,d=t?Ma(i||{},t):i,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&vl(d),ref:t&&t.ref?n&&r?Z(r)?r.concat(An(t)):[r,An(t)]:An(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:o,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ue?l===-1?16:l|16:l,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Kt(e.ssContent),ssFallback:e.ssFallback&&Kt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&s&&ii(c,a.clone(c)),c}function Ut(e=" ",t=0){return re(ns,null,e,t)}function ss(e,t){const n=re(In,null,e);return n.staticCount=t,n}function $i(e="",t=!1){return t?(K(),Xt(mt,null,e)):re(mt,null,e)}function tt(e){return e==null||typeof e=="boolean"?re(mt):Z(e)?re(ue,null,e.slice()):yn(e)?wt(e):re(ns,null,String(e))}function wt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Kt(e)}function di(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(Z(t))n=16;else if(typeof t=="object")if(s&65){const i=t.default;i&&(i._c&&(i._d=!1),di(e,i()),i._c&&(i._d=!0));return}else{n=32;const i=t._;!i&&!il(t)?t._ctx=Ae:i===3&&Ae&&(Ae.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else te(t)?(t={default:t,_ctx:Ae},n=32):(t=String(t),s&64?(n=16,t=[Ut(t)]):n=8);e.children=t,e.shapeFlag|=n}function Ma(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const i in s)if(i==="class")t.class!==s.class&&(t.class=Tt([t.class,s.class]));else if(i==="style")t.style=Ys([t.style,s.style]);else if(qn(i)){const r=t[i],l=s[i];l&&r!==l&&!(Z(r)&&r.includes(l))&&(t[i]=r?[].concat(r,l):l)}else i!==""&&(t[i]=s[i])}return t}function Ze(e,t,n,s=null){ot(e,t,7,[n,s])}const Oa=tl();let Ia=0;function Aa(e,t,n){const s=e.type,i=(t?t.appContext:e.appContext)||Oa,r={uid:Ia++,vnode:e,type:s,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new ro(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:ll(s,i),emitsOptions:hl(s,i),emit:null,emitted:null,propsDefaults:ge,inheritAttrs:s.inheritAttrs,ctx:ge,data:ge,props:ge,attrs:ge,slots:ge,refs:ge,setupState:ge,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return r.ctx={_:r},r.root=t?t.root:r,r.emit=ba.bind(null,r),e.ce&&e.ce(r),r}let Ce=null,Fn,Bs;{const e=Kn(),t=(n,s)=>{let i;return(i=e[n])||(i=e[n]=[]),i.push(s),r=>{i.length>1?i.forEach(l=>l(r)):i[0](r)}};Fn=t("__VUE_INSTANCE_SETTERS__",n=>Ce=n),Bs=t("__VUE_SSR_SETTERS__",n=>bn=n)}const _n=e=>{const t=Ce;return Fn(e),e.scope.on(),()=>{e.scope.off(),Fn(t)}},zi=()=>{Ce&&Ce.scope.off(),Fn(null)};function yl(e){return e.vnode.shapeFlag&4}let bn=!1;function La(e,t=!1,n=!1){t&&Bs(t);const{props:s,children:i}=e.vnode,r=yl(e);ra(e,s,r,t),ca(e,i,n||t);const l=r?Ra(e,t):void 0;return t&&Bs(!1),l}function Ra(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Qo);const{setup:s}=n;if(s){pt();const i=e.setupContext=s.length>1?za(e):null,r=_n(e),l=Sn(s,e,0,[e.props,i]),o=yr(l);if(ht(),r(),(o||e.sp)&&!Vt(e)&&Ur(e),o){if(l.then(zi,zi),t)return l.then(a=>{Bi(e,a)}).catch(a=>{Zn(a,e,0)});e.asyncDep=l}else Bi(e,l)}else bl(e)}function Bi(e,t,n){te(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ye(t)&&(e.setupState=jr(t)),bl(e)}function bl(e,t,n){const s=e.type;e.render||(e.render=s.render||it);{const i=_n(e);pt();try{Jo(e)}finally{ht(),i()}}}const $a={get(e,t){return Te(e,"get",""),e[t]}};function za(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,$a),slots:e.slots,emit:e.emit,expose:t}}function ui(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(jr(Co(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in dn)return dn[n](e)},has(t,n){return n in t||n in dn}})):e.proxy}function Ba(e,t=!0){return te(e)?e.displayName||e.name:e.name||t&&e.__name}function ka(e){return te(e)&&"__vccOpts"in e}const je=(e,t)=>Ao(e,t,bn);function Be(e,t,n){const s=arguments.length;return s===2?ye(t)&&!Z(t)?yn(t)?re(e,null,[t]):re(e,t):re(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&yn(n)&&(n=[n]),re(e,t,n))}const Da="3.5.17";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ks;const ki=typeof window<"u"&&window.trustedTypes;if(ki)try{ks=ki.createPolicy("vue",{createHTML:e=>e})}catch{}const wl=ks?e=>ks.createHTML(e):e=>e,Fa="http://www.w3.org/2000/svg",Na="http://www.w3.org/1998/Math/MathML",ut=typeof document<"u"?document:null,Di=ut&&ut.createElement("template"),ja={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const i=t==="svg"?ut.createElementNS(Fa,e):t==="mathml"?ut.createElementNS(Na,e):n?ut.createElement(e,{is:n}):ut.createElement(e);return e==="select"&&s&&s.multiple!=null&&i.setAttribute("multiple",s.multiple),i},createText:e=>ut.createTextNode(e),createComment:e=>ut.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ut.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,i,r){const l=n?n.previousSibling:t.lastChild;if(i&&(i===r||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),n),!(i===r||!(i=i.nextSibling)););else{Di.innerHTML=wl(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const o=Di.content;if(s==="svg"||s==="mathml"){const a=o.firstChild;for(;a.firstChild;)o.appendChild(a.firstChild);o.removeChild(a)}t.insertBefore(o,n)}return[l?l.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Ha=Symbol("_vtc");function Va(e,t,n){const s=e[Ha];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Fi=Symbol("_vod"),Ga=Symbol("_vsh"),Wa=Symbol(""),qa=/(^|;)\s*display\s*:/;function Ua(e,t,n){const s=e.style,i=we(n);let r=!1;if(n&&!i){if(t)if(we(t))for(const l of t.split(";")){const o=l.slice(0,l.indexOf(":")).trim();n[o]==null&&Ln(s,o,"")}else for(const l in t)n[l]==null&&Ln(s,l,"");for(const l in n)l==="display"&&(r=!0),Ln(s,l,n[l])}else if(i){if(t!==n){const l=s[Wa];l&&(n+=";"+l),s.cssText=n,r=qa.test(n)}}else t&&e.removeAttribute("style");Fi in e&&(e[Fi]=r?s.display:"",e[Ga]&&(s.display="none"))}const Ni=/\s*!important$/;function Ln(e,t,n){if(Z(n))n.forEach(s=>Ln(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Ya(e,t);Ni.test(n)?e.setProperty(Rt(s),n.replace(Ni,""),"important"):e[s]=n}}const ji=["Webkit","Moz","ms"],ps={};function Ya(e,t){const n=ps[t];if(n)return n;let s=qe(t);if(s!=="filter"&&s in e)return ps[t]=s;s=Xn(s);for(let i=0;i<ji.length;i++){const r=ji[i]+s;if(r in e)return ps[t]=r}return t}const Hi="http://www.w3.org/1999/xlink";function Vi(e,t,n,s,i,r=io(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Hi,t.slice(6,t.length)):e.setAttributeNS(Hi,t,n):n==null||r&&!Sr(n)?e.removeAttribute(t):e.setAttribute(t,r?"":gt(n)?String(n):n)}function Gi(e,t,n,s,i){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?wl(n):n);return}const r=e.tagName;if(t==="value"&&r!=="PROGRESS"&&!r.includes("-")){const o=r==="OPTION"?e.getAttribute("value")||"":e.value,a=n==null?e.type==="checkbox"?"on":"":String(n);(o!==a||!("_value"in e))&&(e.value=a),n==null&&e.removeAttribute(t),e._value=n;return}let l=!1;if(n===""||n==null){const o=typeof e[t];o==="boolean"?n=Sr(n):n==null&&o==="string"?(n="",l=!0):o==="number"&&(n=0,l=!0)}try{e[t]=n}catch{}l&&e.removeAttribute(i||t)}function Xa(e,t,n,s){e.addEventListener(t,n,s)}function Ka(e,t,n,s){e.removeEventListener(t,n,s)}const Wi=Symbol("_vei");function Qa(e,t,n,s,i=null){const r=e[Wi]||(e[Wi]={}),l=r[t];if(s&&l)l.value=s;else{const[o,a]=Ja(t);if(s){const d=r[t]=tc(s,i);Xa(e,o,d,a)}else l&&(Ka(e,o,l,a),r[t]=void 0)}}const qi=/(?:Once|Passive|Capture)$/;function Ja(e){let t;if(qi.test(e)){t={};let s;for(;s=e.match(qi);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Rt(e.slice(2)),t]}let hs=0;const Za=Promise.resolve(),ec=()=>hs||(Za.then(()=>hs=0),hs=Date.now());function tc(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;ot(nc(s,n.value),t,5,[s])};return n.value=e,n.attached=ec(),n}function nc(e,t){if(Z(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>i=>!i._stopped&&s&&s(i))}else return t}const Ui=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,sc=(e,t,n,s,i,r)=>{const l=i==="svg";t==="class"?Va(e,s,l):t==="style"?Ua(e,n,s):qn(t)?Ws(t)||Qa(e,t,n,s,r):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):ic(e,t,s,l))?(Gi(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Vi(e,t,s,l,r,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!we(s))?Gi(e,qe(t),s,r,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),Vi(e,t,s,l))};function ic(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&Ui(t)&&te(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const i=e.tagName;if(i==="IMG"||i==="VIDEO"||i==="CANVAS"||i==="SOURCE")return!1}return Ui(t)&&we(n)?!1:t in e}const rc=Me({patchProp:sc},ja);let Yi;function lc(){return Yi||(Yi=ua(rc))}const oc=(...e)=>{const t=lc().createApp(...e),{mount:n}=t;return t.mount=s=>{const i=cc(s);if(!i)return;const r=t._component;!te(r)&&!r.render&&!r.template&&(r.template=i.innerHTML),i.nodeType===1&&(i.textContent="");const l=n(i,!1,ac(i));return i instanceof Element&&(i.removeAttribute("v-cloak"),i.setAttribute("data-v-app","")),l},t};function ac(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function cc(e){return we(e)?document.querySelector(e):e}const Sl="/assets/logo-p8-CIRxZPMi.png",fi=[{name:"打捆机",img:"/images/9YFQ-2.jpg",children:[{name:"9YFQ-2.23方草捆打捆机",img:"/images/9YFQ-2.jpg",params:{型号:"9YFQ-2.23",参数:"XXXX",轴距:"9YFQ-2.23",重量:"1500kg"}},{name:"9YFS-2.23方草捆打捆机",img:"/images/9YFS-2.jpg",params:{型号:"9YFQ-2.23",参数:"XXXX",轴距:"9YFQ-2.23",重量:"1500kg"}},{name:"9YFW-2.2秸秆饲料打捆机",img:"/images/9YFW-2.jpg",params:{型号:"9YFQ-2.23",参数:"XXXX",轴距:"9YFQ-2.23",重量:"1500kg"}},{name:"9YG-2.25圆捆机",img:"/images/9YG-2.jpg",params:{型号:"9YFQ-2.23",参数:"XXXX",轴距:"9YFQ-2.23",重量:"1500kg"}},{name:"粉碎打捆机",img:"/images/粉碎打捆机.jpg",params:{型号:"9YFQ-2.23",参数:"XXXX",轴距:"9YFQ-2.23",重量:"1500kg"}}]},{name:"集捆机",img:"/images/方草捆集捆机.jpg",children:[{name:"方草捆集捆机",img:"/images/方草捆集捆机.jpg",params:{型号:"9YFQ-2.23",参数:"XXXX",轴距:"9YFQ-2.23",重量:"1500kg"}}]},{name:"集垛机",img:"/images/方草捆捡拾集垛机.jpg",children:[{name:"方草捆捡拾集垛机",img:"/images/方草捆捡拾集垛机.jpg",params:{型号:"9YFQ-2.23",参数:"XXXX",轴距:"9YFQ-2.23",重量:"1500kg"}}]}];/*!
  * vue-router v4.2.5
  * (c) 2023 Eduardo San Martin Morote
  * @license MIT
  */const Dt=typeof window<"u";function dc(e){return e.__esModule||e[Symbol.toStringTag]==="Module"}const de=Object.assign;function ms(e,t){const n={};for(const s in t){const i=t[s];n[s]=Xe(i)?i.map(e):e(i)}return n}const fn=()=>{},Xe=Array.isArray,uc=/\/$/,fc=e=>e.replace(uc,"");function gs(e,t,n="/"){let s,i={},r="",l="";const o=t.indexOf("#");let a=t.indexOf("?");return o<a&&o>=0&&(a=-1),a>-1&&(s=t.slice(0,a),r=t.slice(a+1,o>-1?o:t.length),i=e(r)),o>-1&&(s=s||t.slice(0,o),l=t.slice(o,t.length)),s=gc(s??t,n),{fullPath:s+(r&&"?")+r+l,path:s,query:i,hash:l}}function pc(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Xi(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function hc(e,t,n){const s=t.matched.length-1,i=n.matched.length-1;return s>-1&&s===i&&Qt(t.matched[s],n.matched[i])&&xl(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Qt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function xl(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!mc(e[n],t[n]))return!1;return!0}function mc(e,t){return Xe(e)?Ki(e,t):Xe(t)?Ki(t,e):e===t}function Ki(e,t){return Xe(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function gc(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),i=s[s.length-1];(i===".."||i===".")&&s.push("");let r=n.length-1,l,o;for(l=0;l<s.length;l++)if(o=s[l],o!==".")if(o==="..")r>1&&r--;else break;return n.slice(0,r).join("/")+"/"+s.slice(l-(l===s.length?1:0)).join("/")}var wn;(function(e){e.pop="pop",e.push="push"})(wn||(wn={}));var pn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(pn||(pn={}));function vc(e){if(!e)if(Dt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),fc(e)}const yc=/^[^#]+#/;function bc(e,t){return e.replace(yc,"#")+t}function wc(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const is=()=>({left:window.pageXOffset,top:window.pageYOffset});function Sc(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),i=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!i)return;t=wc(i,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.pageXOffset,t.top!=null?t.top:window.pageYOffset)}function Qi(e,t){return(history.state?history.state.position-t:-1)+e}const Ds=new Map;function xc(e,t){Ds.set(e,t)}function _c(e){const t=Ds.get(e);return Ds.delete(e),t}let Ec=()=>location.protocol+"//"+location.host;function _l(e,t){const{pathname:n,search:s,hash:i}=t,r=e.indexOf("#");if(r>-1){let o=i.includes(e.slice(r))?e.slice(r).length:1,a=i.slice(o);return a[0]!=="/"&&(a="/"+a),Xi(a,"")}return Xi(n,e)+s+i}function Tc(e,t,n,s){let i=[],r=[],l=null;const o=({state:f})=>{const m=_l(e,location),g=n.value,S=t.value;let C=0;if(f){if(n.value=m,t.value=f,l&&l===g){l=null;return}C=S?f.position-S.position:0}else s(m);i.forEach(b=>{b(n.value,g,{delta:C,type:wn.pop,direction:C?C>0?pn.forward:pn.back:pn.unknown})})};function a(){l=n.value}function d(f){i.push(f);const m=()=>{const g=i.indexOf(f);g>-1&&i.splice(g,1)};return r.push(m),m}function c(){const{history:f}=window;f.state&&f.replaceState(de({},f.state,{scroll:is()}),"")}function u(){for(const f of r)f();r=[],window.removeEventListener("popstate",o),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",o),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:a,listen:d,destroy:u}}function Ji(e,t,n,s=!1,i=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:i?is():null}}function Cc(e){const{history:t,location:n}=window,s={value:_l(e,n)},i={value:t.state};i.value||r(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function r(a,d,c){const u=e.indexOf("#"),f=u>-1?(n.host&&document.querySelector("base")?e:e.slice(u))+a:Ec()+e+a;try{t[c?"replaceState":"pushState"](d,"",f),i.value=d}catch(m){console.error(m),n[c?"replace":"assign"](f)}}function l(a,d){const c=de({},t.state,Ji(i.value.back,a,i.value.forward,!0),d,{position:i.value.position});r(a,c,!0),s.value=a}function o(a,d){const c=de({},i.value,t.state,{forward:a,scroll:is()});r(c.current,c,!0);const u=de({},Ji(s.value,a,null),{position:c.position+1},d);r(a,u,!1),s.value=a}return{location:s,state:i,push:o,replace:l}}function Pc(e){e=vc(e);const t=Cc(e),n=Tc(e,t.state,t.location,t.replace);function s(r,l=!0){l||n.pauseListeners(),history.go(r)}const i=de({location:"",base:e,go:s,createHref:bc.bind(null,e)},t,n);return Object.defineProperty(i,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(i,"state",{enumerable:!0,get:()=>t.state.value}),i}function Mc(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),Pc(e)}function Oc(e){return typeof e=="string"||e&&typeof e=="object"}function El(e){return typeof e=="string"||typeof e=="symbol"}const yt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},Tl=Symbol("");var Zi;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Zi||(Zi={}));function Jt(e,t){return de(new Error,{type:e,[Tl]:!0},t)}function ct(e,t){return e instanceof Error&&Tl in e&&(t==null||!!(e.type&t))}const er="[^/]+?",Ic={sensitive:!1,strict:!1,start:!0,end:!0},Ac=/[.+*?^${}()[\]/\\]/g;function Lc(e,t){const n=de({},Ic,t),s=[];let i=n.start?"^":"";const r=[];for(const d of e){const c=d.length?[]:[90];n.strict&&!d.length&&(i+="/");for(let u=0;u<d.length;u++){const f=d[u];let m=40+(n.sensitive?.25:0);if(f.type===0)u||(i+="/"),i+=f.value.replace(Ac,"\\$&"),m+=40;else if(f.type===1){const{value:g,repeatable:S,optional:C,regexp:b}=f;r.push({name:g,repeatable:S,optional:C});const h=b||er;if(h!==er){m+=10;try{new RegExp(`(${h})`)}catch(x){throw new Error(`Invalid custom RegExp for param "${g}" (${h}): `+x.message)}}let y=S?`((?:${h})(?:/(?:${h}))*)`:`(${h})`;u||(y=C&&d.length<2?`(?:/${y})`:"/"+y),C&&(y+="?"),i+=y,m+=20,C&&(m+=-8),S&&(m+=-20),h===".*"&&(m+=-50)}c.push(m)}s.push(c)}if(n.strict&&n.end){const d=s.length-1;s[d][s[d].length-1]+=.7000000000000001}n.strict||(i+="/?"),n.end?i+="$":n.strict&&(i+="(?:/|$)");const l=new RegExp(i,n.sensitive?"":"i");function o(d){const c=d.match(l),u={};if(!c)return null;for(let f=1;f<c.length;f++){const m=c[f]||"",g=r[f-1];u[g.name]=m&&g.repeatable?m.split("/"):m}return u}function a(d){let c="",u=!1;for(const f of e){(!u||!c.endsWith("/"))&&(c+="/"),u=!1;for(const m of f)if(m.type===0)c+=m.value;else if(m.type===1){const{value:g,repeatable:S,optional:C}=m,b=g in d?d[g]:"";if(Xe(b)&&!S)throw new Error(`Provided param "${g}" is an array but it is not repeatable (* or + modifiers)`);const h=Xe(b)?b.join("/"):b;if(!h)if(C)f.length<2&&(c.endsWith("/")?c=c.slice(0,-1):u=!0);else throw new Error(`Missing required param "${g}"`);c+=h}}return c||"/"}return{re:l,score:s,keys:r,parse:o,stringify:a}}function Rc(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function $c(e,t){let n=0;const s=e.score,i=t.score;for(;n<s.length&&n<i.length;){const r=Rc(s[n],i[n]);if(r)return r;n++}if(Math.abs(i.length-s.length)===1){if(tr(s))return 1;if(tr(i))return-1}return i.length-s.length}function tr(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const zc={type:0,value:""},Bc=/[a-zA-Z0-9_]/;function kc(e){if(!e)return[[]];if(e==="/")return[[zc]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${d}": ${m}`)}let n=0,s=n;const i=[];let r;function l(){r&&i.push(r),r=[]}let o=0,a,d="",c="";function u(){d&&(n===0?r.push({type:0,value:d}):n===1||n===2||n===3?(r.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${d}) must be alone in its segment. eg: '/:ids+.`),r.push({type:1,value:d,regexp:c,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),d="")}function f(){d+=a}for(;o<e.length;){if(a=e[o++],a==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:a==="/"?(d&&u(),l()):a===":"?(u(),n=1):f();break;case 4:f(),n=s;break;case 1:a==="("?n=2:Bc.test(a)?f():(u(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&o--);break;case 2:a===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+a:n=3:c+=a;break;case 3:u(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&o--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${d}"`),u(),l(),i}function Dc(e,t,n){const s=Lc(kc(e.path),n),i=de(s,{record:e,parent:t,children:[],alias:[]});return t&&!i.record.aliasOf==!t.record.aliasOf&&t.children.push(i),i}function Fc(e,t){const n=[],s=new Map;t=ir({strict:!1,end:!0,sensitive:!1},t);function i(c){return s.get(c)}function r(c,u,f){const m=!f,g=Nc(c);g.aliasOf=f&&f.record;const S=ir(t,c),C=[g];if("alias"in c){const y=typeof c.alias=="string"?[c.alias]:c.alias;for(const x of y)C.push(de({},g,{components:f?f.record.components:g.components,path:x,aliasOf:f?f.record:g}))}let b,h;for(const y of C){const{path:x}=y;if(u&&x[0]!=="/"){const E=u.record.path,k=E[E.length-1]==="/"?"":"/";y.path=u.record.path+(x&&k+x)}if(b=Dc(y,u,S),f?f.alias.push(b):(h=h||b,h!==b&&h.alias.push(b),m&&c.name&&!sr(b)&&l(c.name)),g.children){const E=g.children;for(let k=0;k<E.length;k++)r(E[k],b,f&&f.children[k])}f=f||b,(b.record.components&&Object.keys(b.record.components).length||b.record.name||b.record.redirect)&&a(b)}return h?()=>{l(h)}:fn}function l(c){if(El(c)){const u=s.get(c);u&&(s.delete(c),n.splice(n.indexOf(u),1),u.children.forEach(l),u.alias.forEach(l))}else{const u=n.indexOf(c);u>-1&&(n.splice(u,1),c.record.name&&s.delete(c.record.name),c.children.forEach(l),c.alias.forEach(l))}}function o(){return n}function a(c){let u=0;for(;u<n.length&&$c(c,n[u])>=0&&(c.record.path!==n[u].record.path||!Cl(c,n[u]));)u++;n.splice(u,0,c),c.record.name&&!sr(c)&&s.set(c.record.name,c)}function d(c,u){let f,m={},g,S;if("name"in c&&c.name){if(f=s.get(c.name),!f)throw Jt(1,{location:c});S=f.record.name,m=de(nr(u.params,f.keys.filter(h=>!h.optional).map(h=>h.name)),c.params&&nr(c.params,f.keys.map(h=>h.name))),g=f.stringify(m)}else if("path"in c)g=c.path,f=n.find(h=>h.re.test(g)),f&&(m=f.parse(g),S=f.record.name);else{if(f=u.name?s.get(u.name):n.find(h=>h.re.test(u.path)),!f)throw Jt(1,{location:c,currentLocation:u});S=f.record.name,m=de({},u.params,c.params),g=f.stringify(m)}const C=[];let b=f;for(;b;)C.unshift(b.record),b=b.parent;return{name:S,path:g,params:m,matched:C,meta:Hc(C)}}return e.forEach(c=>r(c)),{addRoute:r,resolve:d,removeRoute:l,getRoutes:o,getRecordMatcher:i}}function nr(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function Nc(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:jc(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}function jc(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function sr(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Hc(e){return e.reduce((t,n)=>de(t,n.meta),{})}function ir(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function Cl(e,t){return t.children.some(n=>n===e||Cl(e,n))}const Pl=/#/g,Vc=/&/g,Gc=/\//g,Wc=/=/g,qc=/\?/g,Ml=/\+/g,Uc=/%5B/g,Yc=/%5D/g,Ol=/%5E/g,Xc=/%60/g,Il=/%7B/g,Kc=/%7C/g,Al=/%7D/g,Qc=/%20/g;function pi(e){return encodeURI(""+e).replace(Kc,"|").replace(Uc,"[").replace(Yc,"]")}function Jc(e){return pi(e).replace(Il,"{").replace(Al,"}").replace(Ol,"^")}function Fs(e){return pi(e).replace(Ml,"%2B").replace(Qc,"+").replace(Pl,"%23").replace(Vc,"%26").replace(Xc,"`").replace(Il,"{").replace(Al,"}").replace(Ol,"^")}function Zc(e){return Fs(e).replace(Wc,"%3D")}function ed(e){return pi(e).replace(Pl,"%23").replace(qc,"%3F")}function td(e){return e==null?"":ed(e).replace(Gc,"%2F")}function Nn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}function nd(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let i=0;i<s.length;++i){const r=s[i].replace(Ml," "),l=r.indexOf("="),o=Nn(l<0?r:r.slice(0,l)),a=l<0?null:Nn(r.slice(l+1));if(o in t){let d=t[o];Xe(d)||(d=t[o]=[d]),d.push(a)}else t[o]=a}return t}function rr(e){let t="";for(let n in e){const s=e[n];if(n=Zc(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(Xe(s)?s.map(r=>r&&Fs(r)):[s&&Fs(s)]).forEach(r=>{r!==void 0&&(t+=(t.length?"&":"")+n,r!=null&&(t+="="+r))})}return t}function sd(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=Xe(s)?s.map(i=>i==null?null:""+i):s==null?s:""+s)}return t}const id=Symbol(""),lr=Symbol(""),hi=Symbol(""),mi=Symbol(""),Ns=Symbol("");function nn(){let e=[];function t(s){return e.push(s),()=>{const i=e.indexOf(s);i>-1&&e.splice(i,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function St(e,t,n,s,i){const r=s&&(s.enterCallbacks[i]=s.enterCallbacks[i]||[]);return()=>new Promise((l,o)=>{const a=u=>{u===!1?o(Jt(4,{from:n,to:t})):u instanceof Error?o(u):Oc(u)?o(Jt(2,{from:t,to:u})):(r&&s.enterCallbacks[i]===r&&typeof u=="function"&&r.push(u),l())},d=e.call(s&&s.instances[i],t,n,a);let c=Promise.resolve(d);e.length<3&&(c=c.then(a)),c.catch(u=>o(u))})}function vs(e,t,n,s){const i=[];for(const r of e)for(const l in r.components){let o=r.components[l];if(!(t!=="beforeRouteEnter"&&!r.instances[l]))if(rd(o)){const d=(o.__vccOpts||o)[t];d&&i.push(St(d,n,s,r,l))}else{let a=o();i.push(()=>a.then(d=>{if(!d)return Promise.reject(new Error(`Couldn't resolve component "${l}" at "${r.path}"`));const c=dc(d)?d.default:d;r.components[l]=c;const f=(c.__vccOpts||c)[t];return f&&St(f,n,s,r,l)()}))}}return i}function rd(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function or(e){const t=rt(hi),n=rt(mi),s=je(()=>t.resolve(nt(e.to))),i=je(()=>{const{matched:a}=s.value,{length:d}=a,c=a[d-1],u=n.matched;if(!c||!u.length)return-1;const f=u.findIndex(Qt.bind(null,c));if(f>-1)return f;const m=ar(a[d-2]);return d>1&&ar(c)===m&&u[u.length-1].path!==m?u.findIndex(Qt.bind(null,a[d-2])):f}),r=je(()=>i.value>-1&&cd(n.params,s.value.params)),l=je(()=>i.value>-1&&i.value===n.matched.length-1&&xl(n.params,s.value.params));function o(a={}){return ad(a)?t[nt(e.replace)?"replace":"push"](nt(e.to)).catch(fn):Promise.resolve()}return{route:s,href:je(()=>s.value.href),isActive:r,isExactActive:l,navigate:o}}const ld=Pt({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:or,setup(e,{slots:t}){const n=Jn(or(e)),{options:s}=rt(hi),i=je(()=>({[cr(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[cr(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const r=t.default&&t.default(n);return e.custom?r:Be("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:i.value},r)}}}),od=ld;function ad(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function cd(e,t){for(const n in t){const s=t[n],i=e[n];if(typeof s=="string"){if(s!==i)return!1}else if(!Xe(i)||i.length!==s.length||s.some((r,l)=>r!==i[l]))return!1}return!0}function ar(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const cr=(e,t,n)=>e??t??n,dd=Pt({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=rt(Ns),i=je(()=>e.route||s.value),r=rt(lr,0),l=je(()=>{let d=nt(r);const{matched:c}=i.value;let u;for(;(u=c[d])&&!u.components;)d++;return d}),o=je(()=>i.value.matched[l.value]);Wt(lr,je(()=>l.value+1)),Wt(id,o),Wt(Ns,i);const a=pe();return qt(()=>[a.value,o.value,e.name],([d,c,u],[f,m,g])=>{c&&(c.instances[u]=d,m&&m!==c&&d&&d===f&&(c.leaveGuards.size||(c.leaveGuards=m.leaveGuards),c.updateGuards.size||(c.updateGuards=m.updateGuards))),d&&c&&(!m||!Qt(c,m)||!f)&&(c.enterCallbacks[u]||[]).forEach(S=>S(d))},{flush:"post"}),()=>{const d=i.value,c=e.name,u=o.value,f=u&&u.components[c];if(!f)return dr(n.default,{Component:f,route:d});const m=u.props[c],g=m?m===!0?d.params:typeof m=="function"?m(d):m:null,C=Be(f,de({},g,t,{onVnodeUnmounted:b=>{b.component.isUnmounted&&(u.instances[c]=null)},ref:a}));return dr(n.default,{Component:C,route:d})||C}}});function dr(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const ud=dd;function fd(e){const t=Fc(e.routes,e),n=e.parseQuery||nd,s=e.stringifyQuery||rr,i=e.history,r=nn(),l=nn(),o=nn(),a=Po(yt);let d=yt;Dt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=ms.bind(null,P=>""+P),u=ms.bind(null,td),f=ms.bind(null,Nn);function m(P,V){let N,W;return El(P)?(N=t.getRecordMatcher(P),W=V):W=P,t.addRoute(W,N)}function g(P){const V=t.getRecordMatcher(P);V&&t.removeRoute(V)}function S(){return t.getRoutes().map(P=>P.record)}function C(P){return!!t.getRecordMatcher(P)}function b(P,V){if(V=de({},V||a.value),typeof P=="string"){const w=gs(n,P,V.path),T=t.resolve({path:w.path},V),I=i.createHref(w.fullPath);return de(w,T,{params:f(T.params),hash:Nn(w.hash),redirectedFrom:void 0,href:I})}let N;if("path"in P)N=de({},P,{path:gs(n,P.path,V.path).path});else{const w=de({},P.params);for(const T in w)w[T]==null&&delete w[T];N=de({},P,{params:u(w)}),V.params=u(V.params)}const W=t.resolve(N,V),fe=P.hash||"";W.params=c(f(W.params));const p=pc(s,de({},P,{hash:Jc(fe),path:W.path})),v=i.createHref(p);return de({fullPath:p,hash:fe,query:s===rr?sd(P.query):P.query||{}},W,{redirectedFrom:void 0,href:v})}function h(P){return typeof P=="string"?gs(n,P,a.value.path):de({},P)}function y(P,V){if(d!==P)return Jt(8,{from:V,to:P})}function x(P){return H(P)}function E(P){return x(de(h(P),{replace:!0}))}function k(P){const V=P.matched[P.matched.length-1];if(V&&V.redirect){const{redirect:N}=V;let W=typeof N=="function"?N(P):N;return typeof W=="string"&&(W=W.includes("?")||W.includes("#")?W=h(W):{path:W},W.params={}),de({query:P.query,hash:P.hash,params:"path"in W?{}:P.params},W)}}function H(P,V){const N=d=b(P),W=a.value,fe=P.state,p=P.force,v=P.replace===!0,w=k(N);if(w)return H(de(h(w),{state:typeof w=="object"?de({},fe,w.state):fe,force:p,replace:v}),V||N);const T=N;T.redirectedFrom=V;let I;return!p&&hc(s,W,N)&&(I=Jt(16,{to:T,from:W}),xe(W,W,!0,!1)),(I?Promise.resolve(I):A(T,W)).catch(M=>ct(M)?ct(M,2)?M:ve(M):D(M,T,W)).then(M=>{if(M){if(ct(M,2))return H(de({replace:v},h(M.to),{state:typeof M.to=="object"?de({},fe,M.to.state):fe,force:p}),V||T)}else M=Y(T,W,!0,v,fe);return B(T,W,M),M})}function z(P,V){const N=y(P,V);return N?Promise.reject(N):Promise.resolve()}function O(P){const V=zt.values().next().value;return V&&typeof V.runWithContext=="function"?V.runWithContext(P):P()}function A(P,V){let N;const[W,fe,p]=pd(P,V);N=vs(W.reverse(),"beforeRouteLeave",P,V);for(const w of W)w.leaveGuards.forEach(T=>{N.push(St(T,P,V))});const v=z.bind(null,P,V);return N.push(v),Ge(N).then(()=>{N=[];for(const w of r.list())N.push(St(w,P,V));return N.push(v),Ge(N)}).then(()=>{N=vs(fe,"beforeRouteUpdate",P,V);for(const w of fe)w.updateGuards.forEach(T=>{N.push(St(T,P,V))});return N.push(v),Ge(N)}).then(()=>{N=[];for(const w of p)if(w.beforeEnter)if(Xe(w.beforeEnter))for(const T of w.beforeEnter)N.push(St(T,P,V));else N.push(St(w.beforeEnter,P,V));return N.push(v),Ge(N)}).then(()=>(P.matched.forEach(w=>w.enterCallbacks={}),N=vs(p,"beforeRouteEnter",P,V),N.push(v),Ge(N))).then(()=>{N=[];for(const w of l.list())N.push(St(w,P,V));return N.push(v),Ge(N)}).catch(w=>ct(w,8)?w:Promise.reject(w))}function B(P,V,N){o.list().forEach(W=>O(()=>W(P,V,N)))}function Y(P,V,N,W,fe){const p=y(P,V);if(p)return p;const v=V===yt,w=Dt?history.state:{};N&&(W||v?i.replace(P.fullPath,de({scroll:v&&w&&w.scroll},fe)):i.push(P.fullPath,fe)),a.value=P,xe(P,V,N,v),ve()}let X;function se(){X||(X=i.listen((P,V,N)=>{if(!En.listening)return;const W=b(P),fe=k(W);if(fe){H(de(fe,{replace:!0}),W).catch(fn);return}d=W;const p=a.value;Dt&&xc(Qi(p.fullPath,N.delta),is()),A(W,p).catch(v=>ct(v,12)?v:ct(v,2)?(H(v.to,W).then(w=>{ct(w,20)&&!N.delta&&N.type===wn.pop&&i.go(-1,!1)}).catch(fn),Promise.reject()):(N.delta&&i.go(-N.delta,!1),D(v,W,p))).then(v=>{v=v||Y(W,p,!1),v&&(N.delta&&!ct(v,8)?i.go(-N.delta,!1):N.type===wn.pop&&ct(v,20)&&i.go(-1,!1)),B(W,p,v)}).catch(fn)}))}let ie=nn(),G=nn(),Q;function D(P,V,N){ve(P);const W=G.list();return W.length?W.forEach(fe=>fe(P,V,N)):console.error(P),Promise.reject(P)}function ae(){return Q&&a.value!==yt?Promise.resolve():new Promise((P,V)=>{ie.add([P,V])})}function ve(P){return Q||(Q=!P,se(),ie.list().forEach(([V,N])=>P?N(P):V()),ie.reset()),P}function xe(P,V,N,W){const{scrollBehavior:fe}=e;if(!Dt||!fe)return Promise.resolve();const p=!N&&_c(Qi(P.fullPath,0))||(W||!N)&&history.state&&history.state.scroll||null;return ni().then(()=>fe(P,V,p)).then(v=>v&&Sc(v)).catch(v=>D(v,P,V))}const Se=P=>i.go(P);let $t;const zt=new Set,En={currentRoute:a,listening:!0,addRoute:m,removeRoute:g,hasRoute:C,getRoutes:S,resolve:b,options:e,push:x,replace:E,go:Se,back:()=>Se(-1),forward:()=>Se(1),beforeEach:r.add,beforeResolve:l.add,afterEach:o.add,onError:G.add,isReady:ae,install(P){const V=this;P.component("RouterLink",od),P.component("RouterView",ud),P.config.globalProperties.$router=V,Object.defineProperty(P.config.globalProperties,"$route",{enumerable:!0,get:()=>nt(a)}),Dt&&!$t&&a.value===yt&&($t=!0,x(i.location).catch(fe=>{}));const N={};for(const fe in yt)Object.defineProperty(N,fe,{get:()=>a.value[fe],enumerable:!0});P.provide(hi,V),P.provide(mi,Dr(N)),P.provide(Ns,a);const W=P.unmount;zt.add(P),P.unmount=function(){zt.delete(P),zt.size<1&&(d=yt,X&&X(),X=null,a.value=yt,$t=!1,Q=!1),W()}}};function Ge(P){return P.reduce((V,N)=>V.then(()=>O(N)),Promise.resolve())}return En}function pd(e,t){const n=[],s=[],i=[],r=Math.max(t.matched.length,e.matched.length);for(let l=0;l<r;l++){const o=t.matched[l];o&&(e.matched.find(d=>Qt(d,o))?s.push(o):n.push(o));const a=e.matched[l];a&&(t.matched.find(d=>Qt(d,a))||i.push(a))}return[n,s,i]}function hd(){return rt(mi)}const Ue=(e,t)=>{const n=e.__vccOpts||e;for(const[s,i]of t)n[s]=i;return n},md={class:"head_main container"},gd={class:"nav"},vd=["onMouseenter"],yd={key:0,class:"items"},bd={class:"item-headimg"},wd=["src","alt"],Sd={class:"children"},xd={class:"item-title"},_d={class:"children-item"},Ed={key:0,class:"space"},Td={__name:"AppHeader",setup(e){const t=pe(!1),n=hd(),s=je(()=>n.path==="/"),i=pe([{text:"首页",link:"/"},{text:"产品中心",link:"/product",items:fi},{text:"新闻动态",link:"#"},{text:"服务支持",link:"/support"},{text:"关于我们",link:"/about"},{text:"加入我们",link:"#"}]);function r(l){l.items&&(t.value=!0)}return(l,o)=>{const a=xn("router-link");return K(),ee(ue,null,[o[3]||(o[3]=_("div",{class:"header"},null,-1)),_("header",{class:Tt(["header",{"menu-hovered":t.value}])},[o[2]||(o[2]=_("div",{class:"head_top"},[_("div",{class:"container fix"},[_("div",{class:"language fix"},[_("div",{class:"lan lan_en r",style:{float:"right"}},[_("a",{href:"http://en.lnzizhu.com/",target:"_blank"})]),_("div",{class:"lan lan_cn r",style:{float:"right"}},[_("a",{href:"http://www.lnzizhu.com/"})])]),_("div",{class:"groups"},[_("div",{class:"groups_hd"},[_("i",{class:"head_map"}),_("span",null,"紫竹站群"),_("i",{class:"head_sj"})]),_("div",{class:"sub"},[_("div",{class:"container"},[_("dl",{class:"fix"},[_("dd",null,[_("a",{href:"http://www.lnzizhu.com/",target:"_blank"},"辽宁紫竹集团有限公司")]),_("dd",null,[_("a",{href:"http://www.lnzizhu.com/sanzha/zh",target:"_blank"},"鞍山紫竹第三轧钢有限公司")]),_("dd",null,[_("a",{href:"http://www.lnzizhu.com/zizhukj/zh",target:"_blank"},"鞍山紫竹科技型钢有限公司")]),_("dd",null,[_("a",{href:"http://www.lnzizhu.com/aszizhu/zh",target:"_blank"},"鞍山紫竹国际贸易有限公司")]),_("dd",null,[_("a",{href:"http://www.lnzzpf.com/",target:"_blank"},"辽宁紫竹高科装备股份有限公司")]),_("dd",null,[_("a",{href:"#",target:"_blank",onclick:"return false;"},"辽宁紫竹智慧农业装备有限公司")]),_("dd",null,[_("a",{href:"http://www.lnzizhu.com/aszzhc/zh",target:"_blank"},"鞍山紫竹重型特钢有限公司")]),_("dd",null,[_("a",{href:"http://www.lnzizhu.com/aszzwz/zh",target:"_blank"},"鞍山紫竹物资有限公司")]),_("dd",null,[_("a",{href:"http://www.lnzizhu.com/aszzhw/zh",target:"_blank"},"鞍山紫竹高科装备国际管理有限公司")])])])])])])],-1)),_("div",md,[o[1]||(o[1]=_("div",{class:"logo flex"},[_("img",{src:Sl,alt:"logo",style:{height:"40px"}}),_("span",{style:{"font-size":"1.1rem","font-weight":"600","margin-left":"8px",color:"#000"}},"紫竹农装")],-1)),_("nav",gd,[(K(!0),ee(ue,null,ke(i.value,d=>(K(),ee("div",{class:"nav-item",key:d.text,onMouseenter:c=>r(d),onMouseleave:o[0]||(o[0]=c=>t.value=!1)},[re(a,{to:d.link,style:{position:"relative","z-index":"2"}},{default:He(()=>[Ut(be(d.text),1)]),_:2},1032,["to"]),d.items?(K(),ee("div",yd,[(K(!0),ee(ue,null,ke(d.items,c=>(K(),ee("div",{key:c.name,class:"item"},[_("div",bd,[_("img",{src:c.img,alt:c.name},null,8,wd)]),_("div",Sd,[_("div",xd,[re(a,{to:"/product"},{default:He(()=>[Ut(be(c.name),1)]),_:2},1024)]),_("div",null,[(K(!0),ee(ue,null,ke(c.children,u=>(K(),ee("div",_d,[re(a,{to:"/product/detail"},{default:He(()=>[Ut(be(u.name),1)]),_:2},1024)]))),256))])])]))),128))])):$i("",!0)],40,vd))),128))])])],2),s.value?$i("",!0):(K(),ee("div",Ed))],64)}}},Cd=Ue(Td,[["__scopeId","data-v-ddb59aa1"]]),Pd={class:"footer"},Md={class:"container grid",style:{"grid-template-columns":"repeat(auto-fit,minmax(160px,1fr))",gap:"32px","align-items":"flex-start"}},Od={style:{"font-weight":"bold","margin-bottom":"0.5em"}},Id={style:{"font-size":"0.8rem","line-height":"2"}},Ad=["href"],Ld={__name:"AppFooter",setup(e){const t=pe([{title:"产品中心",items:fi.map(n=>({text:n.name,link:"/product"}))},{title:"服务支持",items:[{text:"服务网站",link:"#"},{text:"维修保养",link:"#"}]},{title:"新闻中心"},{title:"关于我们",items:[{text:"公司介绍",link:"#"},{text:"加入我们",link:"#"}]}]);return(n,s)=>(K(),ee("footer",Pd,[_("div",Md,[s[0]||(s[0]=_("div",null,[_("img",{src:Sl,alt:"logo",style:{height:"40px"}}),_("div",{style:{margin:"1em 0 0.5em 0","font-weight":"bold"}},"紫竹农装"),_("div",{class:"font-color-gray f-sm"},"专注智能农机与智慧农业")],-1)),(K(!0),ee(ue,null,ke(t.value,i=>(K(),ee("div",{key:i.title},[_("div",Od,be(i.title),1),_("ul",Id,[(K(!0),ee(ue,null,ke(i.items,r=>(K(),ee("li",{key:r.text},[_("a",{href:r.link},be(r.text),9,Ad)]))),128))])]))),128))]),s[1]||(s[1]=_("div",{style:{"text-align":"center","margin-top":"2em","font-size":"0.95rem",opacity:"0.7"}},"© 2025 紫竹农装 版权所有",-1))]))}},Rd=Ue(Ld,[["__scopeId","data-v-ddabe7a2"]]),$d={__name:"App",setup(e){return(t,n)=>{const s=xn("router-view");return K(),ee(ue,null,[re(Cd),re(s),re(Rd)],64)}}},zd="/images/company-build.jpg",Bd={},kd={class:"btn"};function Dd(e,t){return K(),ee("button",kd,[Ko(e.$slots,"default",{},void 0)])}const Fd=Ue(Bd,[["render",Dd],["__scopeId","data-v-8e39a904"]]),Nd="/banner_products.mp4",jd="/intro.mp4";function ur(e){return e!==null&&typeof e=="object"&&"constructor"in e&&e.constructor===Object}function gi(e,t){e===void 0&&(e={}),t===void 0&&(t={});const n=["__proto__","constructor","prototype"];Object.keys(t).filter(s=>n.indexOf(s)<0).forEach(s=>{typeof e[s]>"u"?e[s]=t[s]:ur(t[s])&&ur(e[s])&&Object.keys(t[s]).length>0&&gi(e[s],t[s])})}const Ll={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function lt(){const e=typeof document<"u"?document:{};return gi(e,Ll),e}const Hd={document:Ll,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(e){return typeof setTimeout>"u"?(e(),null):setTimeout(e,0)},cancelAnimationFrame(e){typeof setTimeout>"u"||clearTimeout(e)}};function Le(){const e=typeof window<"u"?window:{};return gi(e,Hd),e}function Vd(e){return e===void 0&&(e=""),e.trim().split(" ").filter(t=>!!t.trim())}function Gd(e){const t=e;Object.keys(t).forEach(n=>{try{t[n]=null}catch{}try{delete t[n]}catch{}})}function Rl(e,t){return t===void 0&&(t=0),setTimeout(e,t)}function jn(){return Date.now()}function Wd(e){const t=Le();let n;return t.getComputedStyle&&(n=t.getComputedStyle(e,null)),!n&&e.currentStyle&&(n=e.currentStyle),n||(n=e.style),n}function qd(e,t){t===void 0&&(t="x");const n=Le();let s,i,r;const l=Wd(e);return n.WebKitCSSMatrix?(i=l.transform||l.webkitTransform,i.split(",").length>6&&(i=i.split(", ").map(o=>o.replace(",",".")).join(", ")),r=new n.WebKitCSSMatrix(i==="none"?"":i)):(r=l.MozTransform||l.OTransform||l.MsTransform||l.msTransform||l.transform||l.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),s=r.toString().split(",")),t==="x"&&(n.WebKitCSSMatrix?i=r.m41:s.length===16?i=parseFloat(s[12]):i=parseFloat(s[4])),t==="y"&&(n.WebKitCSSMatrix?i=r.m42:s.length===16?i=parseFloat(s[13]):i=parseFloat(s[5])),i||0}function Mn(e){return typeof e=="object"&&e!==null&&e.constructor&&Object.prototype.toString.call(e).slice(8,-1)==="Object"}function Ud(e){return typeof window<"u"&&typeof window.HTMLElement<"u"?e instanceof HTMLElement:e&&(e.nodeType===1||e.nodeType===11)}function Ne(){const e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let n=1;n<arguments.length;n+=1){const s=n<0||arguments.length<=n?void 0:arguments[n];if(s!=null&&!Ud(s)){const i=Object.keys(Object(s)).filter(r=>t.indexOf(r)<0);for(let r=0,l=i.length;r<l;r+=1){const o=i[r],a=Object.getOwnPropertyDescriptor(s,o);a!==void 0&&a.enumerable&&(Mn(e[o])&&Mn(s[o])?s[o].__swiper__?e[o]=s[o]:Ne(e[o],s[o]):!Mn(e[o])&&Mn(s[o])?(e[o]={},s[o].__swiper__?e[o]=s[o]:Ne(e[o],s[o])):e[o]=s[o])}}}return e}function On(e,t,n){e.style.setProperty(t,n)}function $l(e){let{swiper:t,targetPosition:n,side:s}=e;const i=Le(),r=-t.translate;let l=null,o;const a=t.params.speed;t.wrapperEl.style.scrollSnapType="none",i.cancelAnimationFrame(t.cssModeFrameID);const d=n>r?"next":"prev",c=(f,m)=>d==="next"&&f>=m||d==="prev"&&f<=m,u=()=>{o=new Date().getTime(),l===null&&(l=o);const f=Math.max(Math.min((o-l)/a,1),0),m=.5-Math.cos(f*Math.PI)/2;let g=r+m*(n-r);if(c(g,n)&&(g=n),t.wrapperEl.scrollTo({[s]:g}),c(g,n)){t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.scrollSnapType="",setTimeout(()=>{t.wrapperEl.style.overflow="",t.wrapperEl.scrollTo({[s]:g})}),i.cancelAnimationFrame(t.cssModeFrameID);return}t.cssModeFrameID=i.requestAnimationFrame(u)};u()}function st(e,t){t===void 0&&(t="");const n=Le(),s=[...e.children];return n.HTMLSlotElement&&e instanceof HTMLSlotElement&&s.push(...e.assignedElements()),t?s.filter(i=>i.matches(t)):s}function Yd(e,t){const n=[t];for(;n.length>0;){const s=n.shift();if(e===s)return!0;n.push(...s.children,...s.shadowRoot?s.shadowRoot.children:[],...s.assignedElements?s.assignedElements():[])}}function Xd(e,t){const n=Le();let s=t.contains(e);return!s&&n.HTMLSlotElement&&t instanceof HTMLSlotElement&&(s=[...t.assignedElements()].includes(e),s||(s=Yd(e,t))),s}function Hn(e){try{console.warn(e);return}catch{}}function Vn(e,t){t===void 0&&(t=[]);const n=document.createElement(e);return n.classList.add(...Array.isArray(t)?t:Vd(t)),n}function Kd(e,t){const n=[];for(;e.previousElementSibling;){const s=e.previousElementSibling;t?s.matches(t)&&n.push(s):n.push(s),e=s}return n}function Qd(e,t){const n=[];for(;e.nextElementSibling;){const s=e.nextElementSibling;t?s.matches(t)&&n.push(s):n.push(s),e=s}return n}function _t(e,t){return Le().getComputedStyle(e,null).getPropertyValue(t)}function Gn(e){let t=e,n;if(t){for(n=0;(t=t.previousSibling)!==null;)t.nodeType===1&&(n+=1);return n}}function zl(e,t){const n=[];let s=e.parentElement;for(;s;)t?s.matches(t)&&n.push(s):n.push(s),s=s.parentElement;return n}function js(e,t,n){const s=Le();return e[t==="width"?"offsetWidth":"offsetHeight"]+parseFloat(s.getComputedStyle(e,null).getPropertyValue(t==="width"?"margin-right":"margin-top"))+parseFloat(s.getComputedStyle(e,null).getPropertyValue(t==="width"?"margin-left":"margin-bottom"))}function _e(e){return(Array.isArray(e)?e:[e]).filter(t=>!!t)}function Wn(e,t){t===void 0&&(t=""),typeof trustedTypes<"u"?e.innerHTML=trustedTypes.createPolicy("html",{createHTML:n=>n}).createHTML(t):e.innerHTML=t}let ys;function Jd(){const e=Le(),t=lt();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}function Bl(){return ys||(ys=Jd()),ys}let bs;function Zd(e){let{userAgent:t}=e===void 0?{}:e;const n=Bl(),s=Le(),i=s.navigator.platform,r=t||s.navigator.userAgent,l={ios:!1,android:!1},o=s.screen.width,a=s.screen.height,d=r.match(/(Android);?[\s\/]+([\d.]+)?/);let c=r.match(/(iPad).*OS\s([\d_]+)/);const u=r.match(/(iPod)(.*OS\s([\d_]+))?/),f=!c&&r.match(/(iPhone\sOS|iOS)\s([\d_]+)/),m=i==="Win32";let g=i==="MacIntel";const S=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!c&&g&&n.touch&&S.indexOf(`${o}x${a}`)>=0&&(c=r.match(/(Version)\/([\d.]+)/),c||(c=[0,1,"13_0_0"]),g=!1),d&&!m&&(l.os="android",l.android=!0),(c||f||u)&&(l.os="ios",l.ios=!0),l}function kl(e){return e===void 0&&(e={}),bs||(bs=Zd(e)),bs}let ws;function eu(){const e=Le(),t=kl();let n=!1;function s(){const o=e.navigator.userAgent.toLowerCase();return o.indexOf("safari")>=0&&o.indexOf("chrome")<0&&o.indexOf("android")<0}if(s()){const o=String(e.navigator.userAgent);if(o.includes("Version/")){const[a,d]=o.split("Version/")[1].split(" ")[0].split(".").map(c=>Number(c));n=a<16||a===16&&d<2}}const i=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),r=s(),l=r||i&&t.ios;return{isSafari:n||r,needPerspectiveFix:n,need3dFix:l,isWebView:i}}function Dl(){return ws||(ws=eu()),ws}function tu(e){let{swiper:t,on:n,emit:s}=e;const i=Le();let r=null,l=null;const o=()=>{!t||t.destroyed||!t.initialized||(s("beforeResize"),s("resize"))},a=()=>{!t||t.destroyed||!t.initialized||(r=new ResizeObserver(u=>{l=i.requestAnimationFrame(()=>{const{width:f,height:m}=t;let g=f,S=m;u.forEach(C=>{let{contentBoxSize:b,contentRect:h,target:y}=C;y&&y!==t.el||(g=h?h.width:(b[0]||b).inlineSize,S=h?h.height:(b[0]||b).blockSize)}),(g!==f||S!==m)&&o()})}),r.observe(t.el))},d=()=>{l&&i.cancelAnimationFrame(l),r&&r.unobserve&&t.el&&(r.unobserve(t.el),r=null)},c=()=>{!t||t.destroyed||!t.initialized||s("orientationchange")};n("init",()=>{if(t.params.resizeObserver&&typeof i.ResizeObserver<"u"){a();return}i.addEventListener("resize",o),i.addEventListener("orientationchange",c)}),n("destroy",()=>{d(),i.removeEventListener("resize",o),i.removeEventListener("orientationchange",c)})}function nu(e){let{swiper:t,extendParams:n,on:s,emit:i}=e;const r=[],l=Le(),o=function(c,u){u===void 0&&(u={});const f=l.MutationObserver||l.WebkitMutationObserver,m=new f(g=>{if(t.__preventObserver__)return;if(g.length===1){i("observerUpdate",g[0]);return}const S=function(){i("observerUpdate",g[0])};l.requestAnimationFrame?l.requestAnimationFrame(S):l.setTimeout(S,0)});m.observe(c,{attributes:typeof u.attributes>"u"?!0:u.attributes,childList:t.isElement||(typeof u.childList>"u"?!0:u).childList,characterData:typeof u.characterData>"u"?!0:u.characterData}),r.push(m)},a=()=>{if(t.params.observer){if(t.params.observeParents){const c=zl(t.hostEl);for(let u=0;u<c.length;u+=1)o(c[u])}o(t.hostEl,{childList:t.params.observeSlideChildren}),o(t.wrapperEl,{attributes:!1})}},d=()=>{r.forEach(c=>{c.disconnect()}),r.splice(0,r.length)};n({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",a),s("destroy",d)}var su={on(e,t,n){const s=this;if(!s.eventsListeners||s.destroyed||typeof t!="function")return s;const i=n?"unshift":"push";return e.split(" ").forEach(r=>{s.eventsListeners[r]||(s.eventsListeners[r]=[]),s.eventsListeners[r][i](t)}),s},once(e,t,n){const s=this;if(!s.eventsListeners||s.destroyed||typeof t!="function")return s;function i(){s.off(e,i),i.__emitterProxy&&delete i.__emitterProxy;for(var r=arguments.length,l=new Array(r),o=0;o<r;o++)l[o]=arguments[o];t.apply(s,l)}return i.__emitterProxy=t,s.on(e,i,n)},onAny(e,t){const n=this;if(!n.eventsListeners||n.destroyed||typeof e!="function")return n;const s=t?"unshift":"push";return n.eventsAnyListeners.indexOf(e)<0&&n.eventsAnyListeners[s](e),n},offAny(e){const t=this;if(!t.eventsListeners||t.destroyed||!t.eventsAnyListeners)return t;const n=t.eventsAnyListeners.indexOf(e);return n>=0&&t.eventsAnyListeners.splice(n,1),t},off(e,t){const n=this;return!n.eventsListeners||n.destroyed||!n.eventsListeners||e.split(" ").forEach(s=>{typeof t>"u"?n.eventsListeners[s]=[]:n.eventsListeners[s]&&n.eventsListeners[s].forEach((i,r)=>{(i===t||i.__emitterProxy&&i.__emitterProxy===t)&&n.eventsListeners[s].splice(r,1)})}),n},emit(){const e=this;if(!e.eventsListeners||e.destroyed||!e.eventsListeners)return e;let t,n,s;for(var i=arguments.length,r=new Array(i),l=0;l<i;l++)r[l]=arguments[l];return typeof r[0]=="string"||Array.isArray(r[0])?(t=r[0],n=r.slice(1,r.length),s=e):(t=r[0].events,n=r[0].data,s=r[0].context||e),n.unshift(s),(Array.isArray(t)?t:t.split(" ")).forEach(a=>{e.eventsAnyListeners&&e.eventsAnyListeners.length&&e.eventsAnyListeners.forEach(d=>{d.apply(s,[a,...n])}),e.eventsListeners&&e.eventsListeners[a]&&e.eventsListeners[a].forEach(d=>{d.apply(s,n)})}),e}};function iu(){const e=this;let t,n;const s=e.el;typeof e.params.width<"u"&&e.params.width!==null?t=e.params.width:t=s.clientWidth,typeof e.params.height<"u"&&e.params.height!==null?n=e.params.height:n=s.clientHeight,!(t===0&&e.isHorizontal()||n===0&&e.isVertical())&&(t=t-parseInt(_t(s,"padding-left")||0,10)-parseInt(_t(s,"padding-right")||0,10),n=n-parseInt(_t(s,"padding-top")||0,10)-parseInt(_t(s,"padding-bottom")||0,10),Number.isNaN(t)&&(t=0),Number.isNaN(n)&&(n=0),Object.assign(e,{width:t,height:n,size:e.isHorizontal()?t:n}))}function ru(){const e=this;function t(A,B){return parseFloat(A.getPropertyValue(e.getDirectionLabel(B))||0)}const n=e.params,{wrapperEl:s,slidesEl:i,size:r,rtlTranslate:l,wrongRTL:o}=e,a=e.virtual&&n.virtual.enabled,d=a?e.virtual.slides.length:e.slides.length,c=st(i,`.${e.params.slideClass}, swiper-slide`),u=a?e.virtual.slides.length:c.length;let f=[];const m=[],g=[];let S=n.slidesOffsetBefore;typeof S=="function"&&(S=n.slidesOffsetBefore.call(e));let C=n.slidesOffsetAfter;typeof C=="function"&&(C=n.slidesOffsetAfter.call(e));const b=e.snapGrid.length,h=e.slidesGrid.length;let y=n.spaceBetween,x=-S,E=0,k=0;if(typeof r>"u")return;typeof y=="string"&&y.indexOf("%")>=0?y=parseFloat(y.replace("%",""))/100*r:typeof y=="string"&&(y=parseFloat(y)),e.virtualSize=-y,c.forEach(A=>{l?A.style.marginLeft="":A.style.marginRight="",A.style.marginBottom="",A.style.marginTop=""}),n.centeredSlides&&n.cssMode&&(On(s,"--swiper-centered-offset-before",""),On(s,"--swiper-centered-offset-after",""));const H=n.grid&&n.grid.rows>1&&e.grid;H?e.grid.initSlides(c):e.grid&&e.grid.unsetSlides();let z;const O=n.slidesPerView==="auto"&&n.breakpoints&&Object.keys(n.breakpoints).filter(A=>typeof n.breakpoints[A].slidesPerView<"u").length>0;for(let A=0;A<u;A+=1){z=0;let B;if(c[A]&&(B=c[A]),H&&e.grid.updateSlide(A,B,c),!(c[A]&&_t(B,"display")==="none")){if(n.slidesPerView==="auto"){O&&(c[A].style[e.getDirectionLabel("width")]="");const Y=getComputedStyle(B),X=B.style.transform,se=B.style.webkitTransform;if(X&&(B.style.transform="none"),se&&(B.style.webkitTransform="none"),n.roundLengths)z=e.isHorizontal()?js(B,"width"):js(B,"height");else{const ie=t(Y,"width"),G=t(Y,"padding-left"),Q=t(Y,"padding-right"),D=t(Y,"margin-left"),ae=t(Y,"margin-right"),ve=Y.getPropertyValue("box-sizing");if(ve&&ve==="border-box")z=ie+D+ae;else{const{clientWidth:xe,offsetWidth:Se}=B;z=ie+G+Q+D+ae+(Se-xe)}}X&&(B.style.transform=X),se&&(B.style.webkitTransform=se),n.roundLengths&&(z=Math.floor(z))}else z=(r-(n.slidesPerView-1)*y)/n.slidesPerView,n.roundLengths&&(z=Math.floor(z)),c[A]&&(c[A].style[e.getDirectionLabel("width")]=`${z}px`);c[A]&&(c[A].swiperSlideSize=z),g.push(z),n.centeredSlides?(x=x+z/2+E/2+y,E===0&&A!==0&&(x=x-r/2-y),A===0&&(x=x-r/2-y),Math.abs(x)<1/1e3&&(x=0),n.roundLengths&&(x=Math.floor(x)),k%n.slidesPerGroup===0&&f.push(x),m.push(x)):(n.roundLengths&&(x=Math.floor(x)),(k-Math.min(e.params.slidesPerGroupSkip,k))%e.params.slidesPerGroup===0&&f.push(x),m.push(x),x=x+z+y),e.virtualSize+=z+y,E=z,k+=1}}if(e.virtualSize=Math.max(e.virtualSize,r)+C,l&&o&&(n.effect==="slide"||n.effect==="coverflow")&&(s.style.width=`${e.virtualSize+y}px`),n.setWrapperSize&&(s.style[e.getDirectionLabel("width")]=`${e.virtualSize+y}px`),H&&e.grid.updateWrapperSize(z,f),!n.centeredSlides){const A=[];for(let B=0;B<f.length;B+=1){let Y=f[B];n.roundLengths&&(Y=Math.floor(Y)),f[B]<=e.virtualSize-r&&A.push(Y)}f=A,Math.floor(e.virtualSize-r)-Math.floor(f[f.length-1])>1&&f.push(e.virtualSize-r)}if(a&&n.loop){const A=g[0]+y;if(n.slidesPerGroup>1){const B=Math.ceil((e.virtual.slidesBefore+e.virtual.slidesAfter)/n.slidesPerGroup),Y=A*n.slidesPerGroup;for(let X=0;X<B;X+=1)f.push(f[f.length-1]+Y)}for(let B=0;B<e.virtual.slidesBefore+e.virtual.slidesAfter;B+=1)n.slidesPerGroup===1&&f.push(f[f.length-1]+A),m.push(m[m.length-1]+A),e.virtualSize+=A}if(f.length===0&&(f=[0]),y!==0){const A=e.isHorizontal()&&l?"marginLeft":e.getDirectionLabel("marginRight");c.filter((B,Y)=>!n.cssMode||n.loop?!0:Y!==c.length-1).forEach(B=>{B.style[A]=`${y}px`})}if(n.centeredSlides&&n.centeredSlidesBounds){let A=0;g.forEach(Y=>{A+=Y+(y||0)}),A-=y;const B=A>r?A-r:0;f=f.map(Y=>Y<=0?-S:Y>B?B+C:Y)}if(n.centerInsufficientSlides){let A=0;g.forEach(Y=>{A+=Y+(y||0)}),A-=y;const B=(n.slidesOffsetBefore||0)+(n.slidesOffsetAfter||0);if(A+B<r){const Y=(r-A-B)/2;f.forEach((X,se)=>{f[se]=X-Y}),m.forEach((X,se)=>{m[se]=X+Y})}}if(Object.assign(e,{slides:c,snapGrid:f,slidesGrid:m,slidesSizesGrid:g}),n.centeredSlides&&n.cssMode&&!n.centeredSlidesBounds){On(s,"--swiper-centered-offset-before",`${-f[0]}px`),On(s,"--swiper-centered-offset-after",`${e.size/2-g[g.length-1]/2}px`);const A=-e.snapGrid[0],B=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map(Y=>Y+A),e.slidesGrid=e.slidesGrid.map(Y=>Y+B)}if(u!==d&&e.emit("slidesLengthChange"),f.length!==b&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),m.length!==h&&e.emit("slidesGridLengthChange"),n.watchSlidesProgress&&e.updateSlidesOffset(),e.emit("slidesUpdated"),!a&&!n.cssMode&&(n.effect==="slide"||n.effect==="fade")){const A=`${n.containerModifierClass}backface-hidden`,B=e.el.classList.contains(A);u<=n.maxBackfaceHiddenSlides?B||e.el.classList.add(A):B&&e.el.classList.remove(A)}}function lu(e){const t=this,n=[],s=t.virtual&&t.params.virtual.enabled;let i=0,r;typeof e=="number"?t.setTransition(e):e===!0&&t.setTransition(t.params.speed);const l=o=>s?t.slides[t.getSlideIndexByData(o)]:t.slides[o];if(t.params.slidesPerView!=="auto"&&t.params.slidesPerView>1)if(t.params.centeredSlides)(t.visibleSlides||[]).forEach(o=>{n.push(o)});else for(r=0;r<Math.ceil(t.params.slidesPerView);r+=1){const o=t.activeIndex+r;if(o>t.slides.length&&!s)break;n.push(l(o))}else n.push(l(t.activeIndex));for(r=0;r<n.length;r+=1)if(typeof n[r]<"u"){const o=n[r].offsetHeight;i=o>i?o:i}(i||i===0)&&(t.wrapperEl.style.height=`${i}px`)}function ou(){const e=this,t=e.slides,n=e.isElement?e.isHorizontal()?e.wrapperEl.offsetLeft:e.wrapperEl.offsetTop:0;for(let s=0;s<t.length;s+=1)t[s].swiperSlideOffset=(e.isHorizontal()?t[s].offsetLeft:t[s].offsetTop)-n-e.cssOverflowAdjustment()}const fr=(e,t,n)=>{t&&!e.classList.contains(n)?e.classList.add(n):!t&&e.classList.contains(n)&&e.classList.remove(n)};function au(e){e===void 0&&(e=this&&this.translate||0);const t=this,n=t.params,{slides:s,rtlTranslate:i,snapGrid:r}=t;if(s.length===0)return;typeof s[0].swiperSlideOffset>"u"&&t.updateSlidesOffset();let l=-e;i&&(l=e),t.visibleSlidesIndexes=[],t.visibleSlides=[];let o=n.spaceBetween;typeof o=="string"&&o.indexOf("%")>=0?o=parseFloat(o.replace("%",""))/100*t.size:typeof o=="string"&&(o=parseFloat(o));for(let a=0;a<s.length;a+=1){const d=s[a];let c=d.swiperSlideOffset;n.cssMode&&n.centeredSlides&&(c-=s[0].swiperSlideOffset);const u=(l+(n.centeredSlides?t.minTranslate():0)-c)/(d.swiperSlideSize+o),f=(l-r[0]+(n.centeredSlides?t.minTranslate():0)-c)/(d.swiperSlideSize+o),m=-(l-c),g=m+t.slidesSizesGrid[a],S=m>=0&&m<=t.size-t.slidesSizesGrid[a],C=m>=0&&m<t.size-1||g>1&&g<=t.size||m<=0&&g>=t.size;C&&(t.visibleSlides.push(d),t.visibleSlidesIndexes.push(a)),fr(d,C,n.slideVisibleClass),fr(d,S,n.slideFullyVisibleClass),d.progress=i?-u:u,d.originalProgress=i?-f:f}}function cu(e){const t=this;if(typeof e>"u"){const c=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*c||0}const n=t.params,s=t.maxTranslate()-t.minTranslate();let{progress:i,isBeginning:r,isEnd:l,progressLoop:o}=t;const a=r,d=l;if(s===0)i=0,r=!0,l=!0;else{i=(e-t.minTranslate())/s;const c=Math.abs(e-t.minTranslate())<1,u=Math.abs(e-t.maxTranslate())<1;r=c||i<=0,l=u||i>=1,c&&(i=0),u&&(i=1)}if(n.loop){const c=t.getSlideIndexByData(0),u=t.getSlideIndexByData(t.slides.length-1),f=t.slidesGrid[c],m=t.slidesGrid[u],g=t.slidesGrid[t.slidesGrid.length-1],S=Math.abs(e);S>=f?o=(S-f)/g:o=(S+g-m)/g,o>1&&(o-=1)}Object.assign(t,{progress:i,progressLoop:o,isBeginning:r,isEnd:l}),(n.watchSlidesProgress||n.centeredSlides&&n.autoHeight)&&t.updateSlidesProgress(e),r&&!a&&t.emit("reachBeginning toEdge"),l&&!d&&t.emit("reachEnd toEdge"),(a&&!r||d&&!l)&&t.emit("fromEdge"),t.emit("progress",i)}const Ss=(e,t,n)=>{t&&!e.classList.contains(n)?e.classList.add(n):!t&&e.classList.contains(n)&&e.classList.remove(n)};function du(){const e=this,{slides:t,params:n,slidesEl:s,activeIndex:i}=e,r=e.virtual&&n.virtual.enabled,l=e.grid&&n.grid&&n.grid.rows>1,o=u=>st(s,`.${n.slideClass}${u}, swiper-slide${u}`)[0];let a,d,c;if(r)if(n.loop){let u=i-e.virtual.slidesBefore;u<0&&(u=e.virtual.slides.length+u),u>=e.virtual.slides.length&&(u-=e.virtual.slides.length),a=o(`[data-swiper-slide-index="${u}"]`)}else a=o(`[data-swiper-slide-index="${i}"]`);else l?(a=t.find(u=>u.column===i),c=t.find(u=>u.column===i+1),d=t.find(u=>u.column===i-1)):a=t[i];a&&(l||(c=Qd(a,`.${n.slideClass}, swiper-slide`)[0],n.loop&&!c&&(c=t[0]),d=Kd(a,`.${n.slideClass}, swiper-slide`)[0],n.loop&&!d===0&&(d=t[t.length-1]))),t.forEach(u=>{Ss(u,u===a,n.slideActiveClass),Ss(u,u===c,n.slideNextClass),Ss(u,u===d,n.slidePrevClass)}),e.emitSlidesClasses()}const Rn=(e,t)=>{if(!e||e.destroyed||!e.params)return;const n=()=>e.isElement?"swiper-slide":`.${e.params.slideClass}`,s=t.closest(n());if(s){let i=s.querySelector(`.${e.params.lazyPreloaderClass}`);!i&&e.isElement&&(s.shadowRoot?i=s.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{s.shadowRoot&&(i=s.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`),i&&i.remove())})),i&&i.remove()}},xs=(e,t)=>{if(!e.slides[t])return;const n=e.slides[t].querySelector('[loading="lazy"]');n&&n.removeAttribute("loading")},Hs=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext;const n=e.slides.length;if(!n||!t||t<0)return;t=Math.min(t,n);const s=e.params.slidesPerView==="auto"?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),i=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){const l=i,o=[l-t];o.push(...Array.from({length:t}).map((a,d)=>l+s+d)),e.slides.forEach((a,d)=>{o.includes(a.column)&&xs(e,d)});return}const r=i+s-1;if(e.params.rewind||e.params.loop)for(let l=i-t;l<=r+t;l+=1){const o=(l%n+n)%n;(o<i||o>r)&&xs(e,o)}else for(let l=Math.max(i-t,0);l<=Math.min(r+t,n-1);l+=1)l!==i&&(l>r||l<i)&&xs(e,l)};function uu(e){const{slidesGrid:t,params:n}=e,s=e.rtlTranslate?e.translate:-e.translate;let i;for(let r=0;r<t.length;r+=1)typeof t[r+1]<"u"?s>=t[r]&&s<t[r+1]-(t[r+1]-t[r])/2?i=r:s>=t[r]&&s<t[r+1]&&(i=r+1):s>=t[r]&&(i=r);return n.normalizeSlideIndex&&(i<0||typeof i>"u")&&(i=0),i}function fu(e){const t=this,n=t.rtlTranslate?t.translate:-t.translate,{snapGrid:s,params:i,activeIndex:r,realIndex:l,snapIndex:o}=t;let a=e,d;const c=m=>{let g=m-t.virtual.slidesBefore;return g<0&&(g=t.virtual.slides.length+g),g>=t.virtual.slides.length&&(g-=t.virtual.slides.length),g};if(typeof a>"u"&&(a=uu(t)),s.indexOf(n)>=0)d=s.indexOf(n);else{const m=Math.min(i.slidesPerGroupSkip,a);d=m+Math.floor((a-m)/i.slidesPerGroup)}if(d>=s.length&&(d=s.length-1),a===r&&!t.params.loop){d!==o&&(t.snapIndex=d,t.emit("snapIndexChange"));return}if(a===r&&t.params.loop&&t.virtual&&t.params.virtual.enabled){t.realIndex=c(a);return}const u=t.grid&&i.grid&&i.grid.rows>1;let f;if(t.virtual&&i.virtual.enabled&&i.loop)f=c(a);else if(u){const m=t.slides.find(S=>S.column===a);let g=parseInt(m.getAttribute("data-swiper-slide-index"),10);Number.isNaN(g)&&(g=Math.max(t.slides.indexOf(m),0)),f=Math.floor(g/i.grid.rows)}else if(t.slides[a]){const m=t.slides[a].getAttribute("data-swiper-slide-index");m?f=parseInt(m,10):f=a}else f=a;Object.assign(t,{previousSnapIndex:o,snapIndex:d,previousRealIndex:l,realIndex:f,previousIndex:r,activeIndex:a}),t.initialized&&Hs(t),t.emit("activeIndexChange"),t.emit("snapIndexChange"),(t.initialized||t.params.runCallbacksOnInit)&&(l!==f&&t.emit("realIndexChange"),t.emit("slideChange"))}function pu(e,t){const n=this,s=n.params;let i=e.closest(`.${s.slideClass}, swiper-slide`);!i&&n.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach(o=>{!i&&o.matches&&o.matches(`.${s.slideClass}, swiper-slide`)&&(i=o)});let r=!1,l;if(i){for(let o=0;o<n.slides.length;o+=1)if(n.slides[o]===i){r=!0,l=o;break}}if(i&&r)n.clickedSlide=i,n.virtual&&n.params.virtual.enabled?n.clickedIndex=parseInt(i.getAttribute("data-swiper-slide-index"),10):n.clickedIndex=l;else{n.clickedSlide=void 0,n.clickedIndex=void 0;return}s.slideToClickedSlide&&n.clickedIndex!==void 0&&n.clickedIndex!==n.activeIndex&&n.slideToClickedSlide()}var hu={updateSize:iu,updateSlides:ru,updateAutoHeight:lu,updateSlidesOffset:ou,updateSlidesProgress:au,updateProgress:cu,updateSlidesClasses:du,updateActiveIndex:fu,updateClickedSlide:pu};function mu(e){e===void 0&&(e=this.isHorizontal()?"x":"y");const t=this,{params:n,rtlTranslate:s,translate:i,wrapperEl:r}=t;if(n.virtualTranslate)return s?-i:i;if(n.cssMode)return i;let l=qd(r,e);return l+=t.cssOverflowAdjustment(),s&&(l=-l),l||0}function gu(e,t){const n=this,{rtlTranslate:s,params:i,wrapperEl:r,progress:l}=n;let o=0,a=0;const d=0;n.isHorizontal()?o=s?-e:e:a=e,i.roundLengths&&(o=Math.floor(o),a=Math.floor(a)),n.previousTranslate=n.translate,n.translate=n.isHorizontal()?o:a,i.cssMode?r[n.isHorizontal()?"scrollLeft":"scrollTop"]=n.isHorizontal()?-o:-a:i.virtualTranslate||(n.isHorizontal()?o-=n.cssOverflowAdjustment():a-=n.cssOverflowAdjustment(),r.style.transform=`translate3d(${o}px, ${a}px, ${d}px)`);let c;const u=n.maxTranslate()-n.minTranslate();u===0?c=0:c=(e-n.minTranslate())/u,c!==l&&n.updateProgress(e),n.emit("setTranslate",n.translate,t)}function vu(){return-this.snapGrid[0]}function yu(){return-this.snapGrid[this.snapGrid.length-1]}function bu(e,t,n,s,i){e===void 0&&(e=0),t===void 0&&(t=this.params.speed),n===void 0&&(n=!0),s===void 0&&(s=!0);const r=this,{params:l,wrapperEl:o}=r;if(r.animating&&l.preventInteractionOnTransition)return!1;const a=r.minTranslate(),d=r.maxTranslate();let c;if(s&&e>a?c=a:s&&e<d?c=d:c=e,r.updateProgress(c),l.cssMode){const u=r.isHorizontal();if(t===0)o[u?"scrollLeft":"scrollTop"]=-c;else{if(!r.support.smoothScroll)return $l({swiper:r,targetPosition:-c,side:u?"left":"top"}),!0;o.scrollTo({[u?"left":"top"]:-c,behavior:"smooth"})}return!0}return t===0?(r.setTransition(0),r.setTranslate(c),n&&(r.emit("beforeTransitionStart",t,i),r.emit("transitionEnd"))):(r.setTransition(t),r.setTranslate(c),n&&(r.emit("beforeTransitionStart",t,i),r.emit("transitionStart")),r.animating||(r.animating=!0,r.onTranslateToWrapperTransitionEnd||(r.onTranslateToWrapperTransitionEnd=function(f){!r||r.destroyed||f.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onTranslateToWrapperTransitionEnd),r.onTranslateToWrapperTransitionEnd=null,delete r.onTranslateToWrapperTransitionEnd,r.animating=!1,n&&r.emit("transitionEnd"))}),r.wrapperEl.addEventListener("transitionend",r.onTranslateToWrapperTransitionEnd))),!0}var wu={getTranslate:mu,setTranslate:gu,minTranslate:vu,maxTranslate:yu,translateTo:bu};function Su(e,t){const n=this;n.params.cssMode||(n.wrapperEl.style.transitionDuration=`${e}ms`,n.wrapperEl.style.transitionDelay=e===0?"0ms":""),n.emit("setTransition",e,t)}function Fl(e){let{swiper:t,runCallbacks:n,direction:s,step:i}=e;const{activeIndex:r,previousIndex:l}=t;let o=s;o||(r>l?o="next":r<l?o="prev":o="reset"),t.emit(`transition${i}`),n&&o==="reset"?t.emit(`slideResetTransition${i}`):n&&r!==l&&(t.emit(`slideChangeTransition${i}`),o==="next"?t.emit(`slideNextTransition${i}`):t.emit(`slidePrevTransition${i}`))}function xu(e,t){e===void 0&&(e=!0);const n=this,{params:s}=n;s.cssMode||(s.autoHeight&&n.updateAutoHeight(),Fl({swiper:n,runCallbacks:e,direction:t,step:"Start"}))}function _u(e,t){e===void 0&&(e=!0);const n=this,{params:s}=n;n.animating=!1,!s.cssMode&&(n.setTransition(0),Fl({swiper:n,runCallbacks:e,direction:t,step:"End"}))}var Eu={setTransition:Su,transitionStart:xu,transitionEnd:_u};function Tu(e,t,n,s,i){e===void 0&&(e=0),n===void 0&&(n=!0),typeof e=="string"&&(e=parseInt(e,10));const r=this;let l=e;l<0&&(l=0);const{params:o,snapGrid:a,slidesGrid:d,previousIndex:c,activeIndex:u,rtlTranslate:f,wrapperEl:m,enabled:g}=r;if(!g&&!s&&!i||r.destroyed||r.animating&&o.preventInteractionOnTransition)return!1;typeof t>"u"&&(t=r.params.speed);const S=Math.min(r.params.slidesPerGroupSkip,l);let C=S+Math.floor((l-S)/r.params.slidesPerGroup);C>=a.length&&(C=a.length-1);const b=-a[C];if(o.normalizeSlideIndex)for(let H=0;H<d.length;H+=1){const z=-Math.floor(b*100),O=Math.floor(d[H]*100),A=Math.floor(d[H+1]*100);typeof d[H+1]<"u"?z>=O&&z<A-(A-O)/2?l=H:z>=O&&z<A&&(l=H+1):z>=O&&(l=H)}if(r.initialized&&l!==u&&(!r.allowSlideNext&&(f?b>r.translate&&b>r.minTranslate():b<r.translate&&b<r.minTranslate())||!r.allowSlidePrev&&b>r.translate&&b>r.maxTranslate()&&(u||0)!==l))return!1;l!==(c||0)&&n&&r.emit("beforeSlideChangeStart"),r.updateProgress(b);let h;l>u?h="next":l<u?h="prev":h="reset";const y=r.virtual&&r.params.virtual.enabled;if(!(y&&i)&&(f&&-b===r.translate||!f&&b===r.translate))return r.updateActiveIndex(l),o.autoHeight&&r.updateAutoHeight(),r.updateSlidesClasses(),o.effect!=="slide"&&r.setTranslate(b),h!=="reset"&&(r.transitionStart(n,h),r.transitionEnd(n,h)),!1;if(o.cssMode){const H=r.isHorizontal(),z=f?b:-b;if(t===0)y&&(r.wrapperEl.style.scrollSnapType="none",r._immediateVirtual=!0),y&&!r._cssModeVirtualInitialSet&&r.params.initialSlide>0?(r._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{m[H?"scrollLeft":"scrollTop"]=z})):m[H?"scrollLeft":"scrollTop"]=z,y&&requestAnimationFrame(()=>{r.wrapperEl.style.scrollSnapType="",r._immediateVirtual=!1});else{if(!r.support.smoothScroll)return $l({swiper:r,targetPosition:z,side:H?"left":"top"}),!0;m.scrollTo({[H?"left":"top"]:z,behavior:"smooth"})}return!0}const k=Dl().isSafari;return y&&!i&&k&&r.isElement&&r.virtual.update(!1,!1,l),r.setTransition(t),r.setTranslate(b),r.updateActiveIndex(l),r.updateSlidesClasses(),r.emit("beforeTransitionStart",t,s),r.transitionStart(n,h),t===0?r.transitionEnd(n,h):r.animating||(r.animating=!0,r.onSlideToWrapperTransitionEnd||(r.onSlideToWrapperTransitionEnd=function(z){!r||r.destroyed||z.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.onSlideToWrapperTransitionEnd=null,delete r.onSlideToWrapperTransitionEnd,r.transitionEnd(n,h))}),r.wrapperEl.addEventListener("transitionend",r.onSlideToWrapperTransitionEnd)),!0}function Cu(e,t,n,s){e===void 0&&(e=0),n===void 0&&(n=!0),typeof e=="string"&&(e=parseInt(e,10));const i=this;if(i.destroyed)return;typeof t>"u"&&(t=i.params.speed);const r=i.grid&&i.params.grid&&i.params.grid.rows>1;let l=e;if(i.params.loop)if(i.virtual&&i.params.virtual.enabled)l=l+i.virtual.slidesBefore;else{let o;if(r){const f=l*i.params.grid.rows;o=i.slides.find(m=>m.getAttribute("data-swiper-slide-index")*1===f).column}else o=i.getSlideIndexByData(l);const a=r?Math.ceil(i.slides.length/i.params.grid.rows):i.slides.length,{centeredSlides:d}=i.params;let c=i.params.slidesPerView;c==="auto"?c=i.slidesPerViewDynamic():(c=Math.ceil(parseFloat(i.params.slidesPerView,10)),d&&c%2===0&&(c=c+1));let u=a-o<c;if(d&&(u=u||o<Math.ceil(c/2)),s&&d&&i.params.slidesPerView!=="auto"&&!r&&(u=!1),u){const f=d?o<i.activeIndex?"prev":"next":o-i.activeIndex-1<i.params.slidesPerView?"next":"prev";i.loopFix({direction:f,slideTo:!0,activeSlideIndex:f==="next"?o+1:o-a+1,slideRealIndex:f==="next"?i.realIndex:void 0})}if(r){const f=l*i.params.grid.rows;l=i.slides.find(m=>m.getAttribute("data-swiper-slide-index")*1===f).column}else l=i.getSlideIndexByData(l)}return requestAnimationFrame(()=>{i.slideTo(l,t,n,s)}),i}function Pu(e,t,n){t===void 0&&(t=!0);const s=this,{enabled:i,params:r,animating:l}=s;if(!i||s.destroyed)return s;typeof e>"u"&&(e=s.params.speed);let o=r.slidesPerGroup;r.slidesPerView==="auto"&&r.slidesPerGroup===1&&r.slidesPerGroupAuto&&(o=Math.max(s.slidesPerViewDynamic("current",!0),1));const a=s.activeIndex<r.slidesPerGroupSkip?1:o,d=s.virtual&&r.virtual.enabled;if(r.loop){if(l&&!d&&r.loopPreventsSliding)return!1;if(s.loopFix({direction:"next"}),s._clientLeft=s.wrapperEl.clientLeft,s.activeIndex===s.slides.length-1&&r.cssMode)return requestAnimationFrame(()=>{s.slideTo(s.activeIndex+a,e,t,n)}),!0}return r.rewind&&s.isEnd?s.slideTo(0,e,t,n):s.slideTo(s.activeIndex+a,e,t,n)}function Mu(e,t,n){t===void 0&&(t=!0);const s=this,{params:i,snapGrid:r,slidesGrid:l,rtlTranslate:o,enabled:a,animating:d}=s;if(!a||s.destroyed)return s;typeof e>"u"&&(e=s.params.speed);const c=s.virtual&&i.virtual.enabled;if(i.loop){if(d&&!c&&i.loopPreventsSliding)return!1;s.loopFix({direction:"prev"}),s._clientLeft=s.wrapperEl.clientLeft}const u=o?s.translate:-s.translate;function f(h){return h<0?-Math.floor(Math.abs(h)):Math.floor(h)}const m=f(u),g=r.map(h=>f(h)),S=i.freeMode&&i.freeMode.enabled;let C=r[g.indexOf(m)-1];if(typeof C>"u"&&(i.cssMode||S)){let h;r.forEach((y,x)=>{m>=y&&(h=x)}),typeof h<"u"&&(C=S?r[h]:r[h>0?h-1:h])}let b=0;if(typeof C<"u"&&(b=l.indexOf(C),b<0&&(b=s.activeIndex-1),i.slidesPerView==="auto"&&i.slidesPerGroup===1&&i.slidesPerGroupAuto&&(b=b-s.slidesPerViewDynamic("previous",!0)+1,b=Math.max(b,0))),i.rewind&&s.isBeginning){const h=s.params.virtual&&s.params.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1;return s.slideTo(h,e,t,n)}else if(i.loop&&s.activeIndex===0&&i.cssMode)return requestAnimationFrame(()=>{s.slideTo(b,e,t,n)}),!0;return s.slideTo(b,e,t,n)}function Ou(e,t,n){t===void 0&&(t=!0);const s=this;if(!s.destroyed)return typeof e>"u"&&(e=s.params.speed),s.slideTo(s.activeIndex,e,t,n)}function Iu(e,t,n,s){t===void 0&&(t=!0),s===void 0&&(s=.5);const i=this;if(i.destroyed)return;typeof e>"u"&&(e=i.params.speed);let r=i.activeIndex;const l=Math.min(i.params.slidesPerGroupSkip,r),o=l+Math.floor((r-l)/i.params.slidesPerGroup),a=i.rtlTranslate?i.translate:-i.translate;if(a>=i.snapGrid[o]){const d=i.snapGrid[o],c=i.snapGrid[o+1];a-d>(c-d)*s&&(r+=i.params.slidesPerGroup)}else{const d=i.snapGrid[o-1],c=i.snapGrid[o];a-d<=(c-d)*s&&(r-=i.params.slidesPerGroup)}return r=Math.max(r,0),r=Math.min(r,i.slidesGrid.length-1),i.slideTo(r,e,t,n)}function Au(){const e=this;if(e.destroyed)return;const{params:t,slidesEl:n}=e,s=t.slidesPerView==="auto"?e.slidesPerViewDynamic():t.slidesPerView;let i=e.getSlideIndexWhenGrid(e.clickedIndex),r;const l=e.isElement?"swiper-slide":`.${t.slideClass}`,o=e.grid&&e.params.grid&&e.params.grid.rows>1;if(t.loop){if(e.animating)return;r=parseInt(e.clickedSlide.getAttribute("data-swiper-slide-index"),10),t.centeredSlides?e.slideToLoop(r):i>(o?(e.slides.length-s)/2-(e.params.grid.rows-1):e.slides.length-s)?(e.loopFix(),i=e.getSlideIndex(st(n,`${l}[data-swiper-slide-index="${r}"]`)[0]),Rl(()=>{e.slideTo(i)})):e.slideTo(i)}else e.slideTo(i)}var Lu={slideTo:Tu,slideToLoop:Cu,slideNext:Pu,slidePrev:Mu,slideReset:Ou,slideToClosest:Iu,slideToClickedSlide:Au};function Ru(e,t){const n=this,{params:s,slidesEl:i}=n;if(!s.loop||n.virtual&&n.params.virtual.enabled)return;const r=()=>{st(i,`.${s.slideClass}, swiper-slide`).forEach((m,g)=>{m.setAttribute("data-swiper-slide-index",g)})},l=()=>{const f=st(i,`.${s.slideBlankClass}`);f.forEach(m=>{m.remove()}),f.length>0&&(n.recalcSlides(),n.updateSlides())},o=n.grid&&s.grid&&s.grid.rows>1;s.loopAddBlankSlides&&(s.slidesPerGroup>1||o)&&l();const a=s.slidesPerGroup*(o?s.grid.rows:1),d=n.slides.length%a!==0,c=o&&n.slides.length%s.grid.rows!==0,u=f=>{for(let m=0;m<f;m+=1){const g=n.isElement?Vn("swiper-slide",[s.slideBlankClass]):Vn("div",[s.slideClass,s.slideBlankClass]);n.slidesEl.append(g)}};if(d){if(s.loopAddBlankSlides){const f=a-n.slides.length%a;u(f),n.recalcSlides(),n.updateSlides()}else Hn("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");r()}else if(c){if(s.loopAddBlankSlides){const f=s.grid.rows-n.slides.length%s.grid.rows;u(f),n.recalcSlides(),n.updateSlides()}else Hn("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");r()}else r();n.loopFix({slideRealIndex:e,direction:s.centeredSlides?void 0:"next",initial:t})}function $u(e){let{slideRealIndex:t,slideTo:n=!0,direction:s,setTranslate:i,activeSlideIndex:r,initial:l,byController:o,byMousewheel:a}=e===void 0?{}:e;const d=this;if(!d.params.loop)return;d.emit("beforeLoopFix");const{slides:c,allowSlidePrev:u,allowSlideNext:f,slidesEl:m,params:g}=d,{centeredSlides:S,initialSlide:C}=g;if(d.allowSlidePrev=!0,d.allowSlideNext=!0,d.virtual&&g.virtual.enabled){n&&(!g.centeredSlides&&d.snapIndex===0?d.slideTo(d.virtual.slides.length,0,!1,!0):g.centeredSlides&&d.snapIndex<g.slidesPerView?d.slideTo(d.virtual.slides.length+d.snapIndex,0,!1,!0):d.snapIndex===d.snapGrid.length-1&&d.slideTo(d.virtual.slidesBefore,0,!1,!0)),d.allowSlidePrev=u,d.allowSlideNext=f,d.emit("loopFix");return}let b=g.slidesPerView;b==="auto"?b=d.slidesPerViewDynamic():(b=Math.ceil(parseFloat(g.slidesPerView,10)),S&&b%2===0&&(b=b+1));const h=g.slidesPerGroupAuto?b:g.slidesPerGroup;let y=S?Math.max(h,Math.ceil(b/2)):h;y%h!==0&&(y+=h-y%h),y+=g.loopAdditionalSlides,d.loopedSlides=y;const x=d.grid&&g.grid&&g.grid.rows>1;c.length<b+y||d.params.effect==="cards"&&c.length<b+y*2?Hn("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):x&&g.grid.fill==="row"&&Hn("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const E=[],k=[],H=x?Math.ceil(c.length/g.grid.rows):c.length,z=l&&H-C<b&&!S;let O=z?C:d.activeIndex;typeof r>"u"?r=d.getSlideIndex(c.find(G=>G.classList.contains(g.slideActiveClass))):O=r;const A=s==="next"||!s,B=s==="prev"||!s;let Y=0,X=0;const ie=(x?c[r].column:r)+(S&&typeof i>"u"?-b/2+.5:0);if(ie<y){Y=Math.max(y-ie,h);for(let G=0;G<y-ie;G+=1){const Q=G-Math.floor(G/H)*H;if(x){const D=H-Q-1;for(let ae=c.length-1;ae>=0;ae-=1)c[ae].column===D&&E.push(ae)}else E.push(H-Q-1)}}else if(ie+b>H-y){X=Math.max(ie-(H-y*2),h),z&&(X=Math.max(X,b-H+C+1));for(let G=0;G<X;G+=1){const Q=G-Math.floor(G/H)*H;x?c.forEach((D,ae)=>{D.column===Q&&k.push(ae)}):k.push(Q)}}if(d.__preventObserver__=!0,requestAnimationFrame(()=>{d.__preventObserver__=!1}),d.params.effect==="cards"&&c.length<b+y*2&&(k.includes(r)&&k.splice(k.indexOf(r),1),E.includes(r)&&E.splice(E.indexOf(r),1)),B&&E.forEach(G=>{c[G].swiperLoopMoveDOM=!0,m.prepend(c[G]),c[G].swiperLoopMoveDOM=!1}),A&&k.forEach(G=>{c[G].swiperLoopMoveDOM=!0,m.append(c[G]),c[G].swiperLoopMoveDOM=!1}),d.recalcSlides(),g.slidesPerView==="auto"?d.updateSlides():x&&(E.length>0&&B||k.length>0&&A)&&d.slides.forEach((G,Q)=>{d.grid.updateSlide(Q,G,d.slides)}),g.watchSlidesProgress&&d.updateSlidesOffset(),n){if(E.length>0&&B){if(typeof t>"u"){const G=d.slidesGrid[O],D=d.slidesGrid[O+Y]-G;a?d.setTranslate(d.translate-D):(d.slideTo(O+Math.ceil(Y),0,!1,!0),i&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-D,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-D))}else if(i){const G=x?E.length/g.grid.rows:E.length;d.slideTo(d.activeIndex+G,0,!1,!0),d.touchEventsData.currentTranslate=d.translate}}else if(k.length>0&&A)if(typeof t>"u"){const G=d.slidesGrid[O],D=d.slidesGrid[O-X]-G;a?d.setTranslate(d.translate-D):(d.slideTo(O-X,0,!1,!0),i&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-D,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-D))}else{const G=x?k.length/g.grid.rows:k.length;d.slideTo(d.activeIndex-G,0,!1,!0)}}if(d.allowSlidePrev=u,d.allowSlideNext=f,d.controller&&d.controller.control&&!o){const G={slideRealIndex:t,direction:s,setTranslate:i,activeSlideIndex:r,byController:!0};Array.isArray(d.controller.control)?d.controller.control.forEach(Q=>{!Q.destroyed&&Q.params.loop&&Q.loopFix({...G,slideTo:Q.params.slidesPerView===g.slidesPerView?n:!1})}):d.controller.control instanceof d.constructor&&d.controller.control.params.loop&&d.controller.control.loopFix({...G,slideTo:d.controller.control.params.slidesPerView===g.slidesPerView?n:!1})}d.emit("loopFix")}function zu(){const e=this,{params:t,slidesEl:n}=e;if(!t.loop||!n||e.virtual&&e.params.virtual.enabled)return;e.recalcSlides();const s=[];e.slides.forEach(i=>{const r=typeof i.swiperSlideIndex>"u"?i.getAttribute("data-swiper-slide-index")*1:i.swiperSlideIndex;s[r]=i}),e.slides.forEach(i=>{i.removeAttribute("data-swiper-slide-index")}),s.forEach(i=>{n.append(i)}),e.recalcSlides(),e.slideTo(e.realIndex,0)}var Bu={loopCreate:Ru,loopFix:$u,loopDestroy:zu};function ku(e){const t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;const n=t.params.touchEventsTarget==="container"?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),n.style.cursor="move",n.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1})}function Du(){const e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e[e.params.touchEventsTarget==="container"?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1}))}var Fu={setGrabCursor:ku,unsetGrabCursor:Du};function Nu(e,t){t===void 0&&(t=this);function n(s){if(!s||s===lt()||s===Le())return null;s.assignedSlot&&(s=s.assignedSlot);const i=s.closest(e);return!i&&!s.getRootNode?null:i||n(s.getRootNode().host)}return n(t)}function pr(e,t,n){const s=Le(),{params:i}=e,r=i.edgeSwipeDetection,l=i.edgeSwipeThreshold;return r&&(n<=l||n>=s.innerWidth-l)?r==="prevent"?(t.preventDefault(),!0):!1:!0}function ju(e){const t=this,n=lt();let s=e;s.originalEvent&&(s=s.originalEvent);const i=t.touchEventsData;if(s.type==="pointerdown"){if(i.pointerId!==null&&i.pointerId!==s.pointerId)return;i.pointerId=s.pointerId}else s.type==="touchstart"&&s.targetTouches.length===1&&(i.touchId=s.targetTouches[0].identifier);if(s.type==="touchstart"){pr(t,s,s.targetTouches[0].pageX);return}const{params:r,touches:l,enabled:o}=t;if(!o||!r.simulateTouch&&s.pointerType==="mouse"||t.animating&&r.preventInteractionOnTransition)return;!t.animating&&r.cssMode&&r.loop&&t.loopFix();let a=s.target;if(r.touchEventsTarget==="wrapper"&&!Xd(a,t.wrapperEl)||"which"in s&&s.which===3||"button"in s&&s.button>0||i.isTouched&&i.isMoved)return;const d=!!r.noSwipingClass&&r.noSwipingClass!=="",c=s.composedPath?s.composedPath():s.path;d&&s.target&&s.target.shadowRoot&&c&&(a=c[0]);const u=r.noSwipingSelector?r.noSwipingSelector:`.${r.noSwipingClass}`,f=!!(s.target&&s.target.shadowRoot);if(r.noSwiping&&(f?Nu(u,a):a.closest(u))){t.allowClick=!0;return}if(r.swipeHandler&&!a.closest(r.swipeHandler))return;l.currentX=s.pageX,l.currentY=s.pageY;const m=l.currentX,g=l.currentY;if(!pr(t,s,m))return;Object.assign(i,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),l.startX=m,l.startY=g,i.touchStartTime=jn(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,r.threshold>0&&(i.allowThresholdMove=!1);let S=!0;a.matches(i.focusableElements)&&(S=!1,a.nodeName==="SELECT"&&(i.isTouched=!1)),n.activeElement&&n.activeElement.matches(i.focusableElements)&&n.activeElement!==a&&(s.pointerType==="mouse"||s.pointerType!=="mouse"&&!a.matches(i.focusableElements))&&n.activeElement.blur();const C=S&&t.allowTouchMove&&r.touchStartPreventDefault;(r.touchStartForcePreventDefault||C)&&!a.isContentEditable&&s.preventDefault(),r.freeMode&&r.freeMode.enabled&&t.freeMode&&t.animating&&!r.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",s)}function Hu(e){const t=lt(),n=this,s=n.touchEventsData,{params:i,touches:r,rtlTranslate:l,enabled:o}=n;if(!o||!i.simulateTouch&&e.pointerType==="mouse")return;let a=e;if(a.originalEvent&&(a=a.originalEvent),a.type==="pointermove"&&(s.touchId!==null||a.pointerId!==s.pointerId))return;let d;if(a.type==="touchmove"){if(d=[...a.changedTouches].find(E=>E.identifier===s.touchId),!d||d.identifier!==s.touchId)return}else d=a;if(!s.isTouched){s.startMoving&&s.isScrolling&&n.emit("touchMoveOpposite",a);return}const c=d.pageX,u=d.pageY;if(a.preventedByNestedSwiper){r.startX=c,r.startY=u;return}if(!n.allowTouchMove){a.target.matches(s.focusableElements)||(n.allowClick=!1),s.isTouched&&(Object.assign(r,{startX:c,startY:u,currentX:c,currentY:u}),s.touchStartTime=jn());return}if(i.touchReleaseOnEdges&&!i.loop)if(n.isVertical()){if(u<r.startY&&n.translate<=n.maxTranslate()||u>r.startY&&n.translate>=n.minTranslate()){s.isTouched=!1,s.isMoved=!1;return}}else{if(l&&(c>r.startX&&-n.translate<=n.maxTranslate()||c<r.startX&&-n.translate>=n.minTranslate()))return;if(!l&&(c<r.startX&&n.translate<=n.maxTranslate()||c>r.startX&&n.translate>=n.minTranslate()))return}if(t.activeElement&&t.activeElement.matches(s.focusableElements)&&t.activeElement!==a.target&&a.pointerType!=="mouse"&&t.activeElement.blur(),t.activeElement&&a.target===t.activeElement&&a.target.matches(s.focusableElements)){s.isMoved=!0,n.allowClick=!1;return}s.allowTouchCallbacks&&n.emit("touchMove",a),r.previousX=r.currentX,r.previousY=r.currentY,r.currentX=c,r.currentY=u;const f=r.currentX-r.startX,m=r.currentY-r.startY;if(n.params.threshold&&Math.sqrt(f**2+m**2)<n.params.threshold)return;if(typeof s.isScrolling>"u"){let E;n.isHorizontal()&&r.currentY===r.startY||n.isVertical()&&r.currentX===r.startX?s.isScrolling=!1:f*f+m*m>=25&&(E=Math.atan2(Math.abs(m),Math.abs(f))*180/Math.PI,s.isScrolling=n.isHorizontal()?E>i.touchAngle:90-E>i.touchAngle)}if(s.isScrolling&&n.emit("touchMoveOpposite",a),typeof s.startMoving>"u"&&(r.currentX!==r.startX||r.currentY!==r.startY)&&(s.startMoving=!0),s.isScrolling||a.type==="touchmove"&&s.preventTouchMoveFromPointerMove){s.isTouched=!1;return}if(!s.startMoving)return;n.allowClick=!1,!i.cssMode&&a.cancelable&&a.preventDefault(),i.touchMoveStopPropagation&&!i.nested&&a.stopPropagation();let g=n.isHorizontal()?f:m,S=n.isHorizontal()?r.currentX-r.previousX:r.currentY-r.previousY;i.oneWayMovement&&(g=Math.abs(g)*(l?1:-1),S=Math.abs(S)*(l?1:-1)),r.diff=g,g*=i.touchRatio,l&&(g=-g,S=-S);const C=n.touchesDirection;n.swipeDirection=g>0?"prev":"next",n.touchesDirection=S>0?"prev":"next";const b=n.params.loop&&!i.cssMode,h=n.touchesDirection==="next"&&n.allowSlideNext||n.touchesDirection==="prev"&&n.allowSlidePrev;if(!s.isMoved){if(b&&h&&n.loopFix({direction:n.swipeDirection}),s.startTranslate=n.getTranslate(),n.setTransition(0),n.animating){const E=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});n.wrapperEl.dispatchEvent(E)}s.allowMomentumBounce=!1,i.grabCursor&&(n.allowSlideNext===!0||n.allowSlidePrev===!0)&&n.setGrabCursor(!0),n.emit("sliderFirstMove",a)}if(new Date().getTime(),i._loopSwapReset!==!1&&s.isMoved&&s.allowThresholdMove&&C!==n.touchesDirection&&b&&h&&Math.abs(g)>=1){Object.assign(r,{startX:c,startY:u,currentX:c,currentY:u,startTranslate:s.currentTranslate}),s.loopSwapReset=!0,s.startTranslate=s.currentTranslate;return}n.emit("sliderMove",a),s.isMoved=!0,s.currentTranslate=g+s.startTranslate;let y=!0,x=i.resistanceRatio;if(i.touchReleaseOnEdges&&(x=0),g>0?(b&&h&&s.allowThresholdMove&&s.currentTranslate>(i.centeredSlides?n.minTranslate()-n.slidesSizesGrid[n.activeIndex+1]-(i.slidesPerView!=="auto"&&n.slides.length-i.slidesPerView>=2?n.slidesSizesGrid[n.activeIndex+1]+n.params.spaceBetween:0)-n.params.spaceBetween:n.minTranslate())&&n.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),s.currentTranslate>n.minTranslate()&&(y=!1,i.resistance&&(s.currentTranslate=n.minTranslate()-1+(-n.minTranslate()+s.startTranslate+g)**x))):g<0&&(b&&h&&s.allowThresholdMove&&s.currentTranslate<(i.centeredSlides?n.maxTranslate()+n.slidesSizesGrid[n.slidesSizesGrid.length-1]+n.params.spaceBetween+(i.slidesPerView!=="auto"&&n.slides.length-i.slidesPerView>=2?n.slidesSizesGrid[n.slidesSizesGrid.length-1]+n.params.spaceBetween:0):n.maxTranslate())&&n.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:n.slides.length-(i.slidesPerView==="auto"?n.slidesPerViewDynamic():Math.ceil(parseFloat(i.slidesPerView,10)))}),s.currentTranslate<n.maxTranslate()&&(y=!1,i.resistance&&(s.currentTranslate=n.maxTranslate()+1-(n.maxTranslate()-s.startTranslate-g)**x))),y&&(a.preventedByNestedSwiper=!0),!n.allowSlideNext&&n.swipeDirection==="next"&&s.currentTranslate<s.startTranslate&&(s.currentTranslate=s.startTranslate),!n.allowSlidePrev&&n.swipeDirection==="prev"&&s.currentTranslate>s.startTranslate&&(s.currentTranslate=s.startTranslate),!n.allowSlidePrev&&!n.allowSlideNext&&(s.currentTranslate=s.startTranslate),i.threshold>0)if(Math.abs(g)>i.threshold||s.allowThresholdMove){if(!s.allowThresholdMove){s.allowThresholdMove=!0,r.startX=r.currentX,r.startY=r.currentY,s.currentTranslate=s.startTranslate,r.diff=n.isHorizontal()?r.currentX-r.startX:r.currentY-r.startY;return}}else{s.currentTranslate=s.startTranslate;return}!i.followFinger||i.cssMode||((i.freeMode&&i.freeMode.enabled&&n.freeMode||i.watchSlidesProgress)&&(n.updateActiveIndex(),n.updateSlidesClasses()),i.freeMode&&i.freeMode.enabled&&n.freeMode&&n.freeMode.onTouchMove(),n.updateProgress(s.currentTranslate),n.setTranslate(s.currentTranslate))}function Vu(e){const t=this,n=t.touchEventsData;let s=e;s.originalEvent&&(s=s.originalEvent);let i;if(s.type==="touchend"||s.type==="touchcancel"){if(i=[...s.changedTouches].find(E=>E.identifier===n.touchId),!i||i.identifier!==n.touchId)return}else{if(n.touchId!==null||s.pointerId!==n.pointerId)return;i=s}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(s.type)&&!(["pointercancel","contextmenu"].includes(s.type)&&(t.browser.isSafari||t.browser.isWebView)))return;n.pointerId=null,n.touchId=null;const{params:l,touches:o,rtlTranslate:a,slidesGrid:d,enabled:c}=t;if(!c||!l.simulateTouch&&s.pointerType==="mouse")return;if(n.allowTouchCallbacks&&t.emit("touchEnd",s),n.allowTouchCallbacks=!1,!n.isTouched){n.isMoved&&l.grabCursor&&t.setGrabCursor(!1),n.isMoved=!1,n.startMoving=!1;return}l.grabCursor&&n.isMoved&&n.isTouched&&(t.allowSlideNext===!0||t.allowSlidePrev===!0)&&t.setGrabCursor(!1);const u=jn(),f=u-n.touchStartTime;if(t.allowClick){const E=s.path||s.composedPath&&s.composedPath();t.updateClickedSlide(E&&E[0]||s.target,E),t.emit("tap click",s),f<300&&u-n.lastClickTime<300&&t.emit("doubleTap doubleClick",s)}if(n.lastClickTime=jn(),Rl(()=>{t.destroyed||(t.allowClick=!0)}),!n.isTouched||!n.isMoved||!t.swipeDirection||o.diff===0&&!n.loopSwapReset||n.currentTranslate===n.startTranslate&&!n.loopSwapReset){n.isTouched=!1,n.isMoved=!1,n.startMoving=!1;return}n.isTouched=!1,n.isMoved=!1,n.startMoving=!1;let m;if(l.followFinger?m=a?t.translate:-t.translate:m=-n.currentTranslate,l.cssMode)return;if(l.freeMode&&l.freeMode.enabled){t.freeMode.onTouchEnd({currentPos:m});return}const g=m>=-t.maxTranslate()&&!t.params.loop;let S=0,C=t.slidesSizesGrid[0];for(let E=0;E<d.length;E+=E<l.slidesPerGroupSkip?1:l.slidesPerGroup){const k=E<l.slidesPerGroupSkip-1?1:l.slidesPerGroup;typeof d[E+k]<"u"?(g||m>=d[E]&&m<d[E+k])&&(S=E,C=d[E+k]-d[E]):(g||m>=d[E])&&(S=E,C=d[d.length-1]-d[d.length-2])}let b=null,h=null;l.rewind&&(t.isBeginning?h=l.virtual&&l.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(b=0));const y=(m-d[S])/C,x=S<l.slidesPerGroupSkip-1?1:l.slidesPerGroup;if(f>l.longSwipesMs){if(!l.longSwipes){t.slideTo(t.activeIndex);return}t.swipeDirection==="next"&&(y>=l.longSwipesRatio?t.slideTo(l.rewind&&t.isEnd?b:S+x):t.slideTo(S)),t.swipeDirection==="prev"&&(y>1-l.longSwipesRatio?t.slideTo(S+x):h!==null&&y<0&&Math.abs(y)>l.longSwipesRatio?t.slideTo(h):t.slideTo(S))}else{if(!l.shortSwipes){t.slideTo(t.activeIndex);return}t.navigation&&(s.target===t.navigation.nextEl||s.target===t.navigation.prevEl)?s.target===t.navigation.nextEl?t.slideTo(S+x):t.slideTo(S):(t.swipeDirection==="next"&&t.slideTo(b!==null?b:S+x),t.swipeDirection==="prev"&&t.slideTo(h!==null?h:S))}}function hr(){const e=this,{params:t,el:n}=e;if(n&&n.offsetWidth===0)return;t.breakpoints&&e.setBreakpoint();const{allowSlideNext:s,allowSlidePrev:i,snapGrid:r}=e,l=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();const o=l&&t.loop;(t.slidesPerView==="auto"||t.slidesPerView>1)&&e.isEnd&&!e.isBeginning&&!e.params.centeredSlides&&!o?e.slideTo(e.slides.length-1,0,!1,!0):e.params.loop&&!l?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout(()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()},500)),e.allowSlidePrev=i,e.allowSlideNext=s,e.params.watchOverflow&&r!==e.snapGrid&&e.checkOverflow()}function Gu(e){const t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function Wu(){const e=this,{wrapperEl:t,rtlTranslate:n,enabled:s}=e;if(!s)return;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,e.translate===0&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();let i;const r=e.maxTranslate()-e.minTranslate();r===0?i=0:i=(e.translate-e.minTranslate())/r,i!==e.progress&&e.updateProgress(n?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}function qu(e){const t=this;Rn(t,e.target),!(t.params.cssMode||t.params.slidesPerView!=="auto"&&!t.params.autoHeight)&&t.update()}function Uu(){const e=this;e.documentTouchHandlerProceeded||(e.documentTouchHandlerProceeded=!0,e.params.touchReleaseOnEdges&&(e.el.style.touchAction="auto"))}const Nl=(e,t)=>{const n=lt(),{params:s,el:i,wrapperEl:r,device:l}=e,o=!!s.nested,a=t==="on"?"addEventListener":"removeEventListener",d=t;!i||typeof i=="string"||(n[a]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:o}),i[a]("touchstart",e.onTouchStart,{passive:!1}),i[a]("pointerdown",e.onTouchStart,{passive:!1}),n[a]("touchmove",e.onTouchMove,{passive:!1,capture:o}),n[a]("pointermove",e.onTouchMove,{passive:!1,capture:o}),n[a]("touchend",e.onTouchEnd,{passive:!0}),n[a]("pointerup",e.onTouchEnd,{passive:!0}),n[a]("pointercancel",e.onTouchEnd,{passive:!0}),n[a]("touchcancel",e.onTouchEnd,{passive:!0}),n[a]("pointerout",e.onTouchEnd,{passive:!0}),n[a]("pointerleave",e.onTouchEnd,{passive:!0}),n[a]("contextmenu",e.onTouchEnd,{passive:!0}),(s.preventClicks||s.preventClicksPropagation)&&i[a]("click",e.onClick,!0),s.cssMode&&r[a]("scroll",e.onScroll),s.updateOnWindowResize?e[d](l.ios||l.android?"resize orientationchange observerUpdate":"resize observerUpdate",hr,!0):e[d]("observerUpdate",hr,!0),i[a]("load",e.onLoad,{capture:!0}))};function Yu(){const e=this,{params:t}=e;e.onTouchStart=ju.bind(e),e.onTouchMove=Hu.bind(e),e.onTouchEnd=Vu.bind(e),e.onDocumentTouchStart=Uu.bind(e),t.cssMode&&(e.onScroll=Wu.bind(e)),e.onClick=Gu.bind(e),e.onLoad=qu.bind(e),Nl(e,"on")}function Xu(){Nl(this,"off")}var Ku={attachEvents:Yu,detachEvents:Xu};const mr=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;function Qu(){const e=this,{realIndex:t,initialized:n,params:s,el:i}=e,r=s.breakpoints;if(!r||r&&Object.keys(r).length===0)return;const l=lt(),o=s.breakpointsBase==="window"||!s.breakpointsBase?s.breakpointsBase:"container",a=["window","container"].includes(s.breakpointsBase)||!s.breakpointsBase?e.el:l.querySelector(s.breakpointsBase),d=e.getBreakpoint(r,o,a);if(!d||e.currentBreakpoint===d)return;const u=(d in r?r[d]:void 0)||e.originalParams,f=mr(e,s),m=mr(e,u),g=e.params.grabCursor,S=u.grabCursor,C=s.enabled;f&&!m?(i.classList.remove(`${s.containerModifierClass}grid`,`${s.containerModifierClass}grid-column`),e.emitContainerClasses()):!f&&m&&(i.classList.add(`${s.containerModifierClass}grid`),(u.grid.fill&&u.grid.fill==="column"||!u.grid.fill&&s.grid.fill==="column")&&i.classList.add(`${s.containerModifierClass}grid-column`),e.emitContainerClasses()),g&&!S?e.unsetGrabCursor():!g&&S&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(k=>{if(typeof u[k]>"u")return;const H=s[k]&&s[k].enabled,z=u[k]&&u[k].enabled;H&&!z&&e[k].disable(),!H&&z&&e[k].enable()});const b=u.direction&&u.direction!==s.direction,h=s.loop&&(u.slidesPerView!==s.slidesPerView||b),y=s.loop;b&&n&&e.changeDirection(),Ne(e.params,u);const x=e.params.enabled,E=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),C&&!x?e.disable():!C&&x&&e.enable(),e.currentBreakpoint=d,e.emit("_beforeBreakpoint",u),n&&(h?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!y&&E?(e.loopCreate(t),e.updateSlides()):y&&!E&&e.loopDestroy()),e.emit("breakpoint",u)}function Ju(e,t,n){if(t===void 0&&(t="window"),!e||t==="container"&&!n)return;let s=!1;const i=Le(),r=t==="window"?i.innerHeight:n.clientHeight,l=Object.keys(e).map(o=>{if(typeof o=="string"&&o.indexOf("@")===0){const a=parseFloat(o.substr(1));return{value:r*a,point:o}}return{value:o,point:o}});l.sort((o,a)=>parseInt(o.value,10)-parseInt(a.value,10));for(let o=0;o<l.length;o+=1){const{point:a,value:d}=l[o];t==="window"?i.matchMedia(`(min-width: ${d}px)`).matches&&(s=a):d<=n.clientWidth&&(s=a)}return s||"max"}var Zu={setBreakpoint:Qu,getBreakpoint:Ju};function ef(e,t){const n=[];return e.forEach(s=>{typeof s=="object"?Object.keys(s).forEach(i=>{s[i]&&n.push(t+i)}):typeof s=="string"&&n.push(t+s)}),n}function tf(){const e=this,{classNames:t,params:n,rtl:s,el:i,device:r}=e,l=ef(["initialized",n.direction,{"free-mode":e.params.freeMode&&n.freeMode.enabled},{autoheight:n.autoHeight},{rtl:s},{grid:n.grid&&n.grid.rows>1},{"grid-column":n.grid&&n.grid.rows>1&&n.grid.fill==="column"},{android:r.android},{ios:r.ios},{"css-mode":n.cssMode},{centered:n.cssMode&&n.centeredSlides},{"watch-progress":n.watchSlidesProgress}],n.containerModifierClass);t.push(...l),i.classList.add(...t),e.emitContainerClasses()}function nf(){const e=this,{el:t,classNames:n}=e;!t||typeof t=="string"||(t.classList.remove(...n),e.emitContainerClasses())}var sf={addClasses:tf,removeClasses:nf};function rf(){const e=this,{isLocked:t,params:n}=e,{slidesOffsetBefore:s}=n;if(s){const i=e.slides.length-1,r=e.slidesGrid[i]+e.slidesSizesGrid[i]+s*2;e.isLocked=e.size>r}else e.isLocked=e.snapGrid.length===1;n.allowSlideNext===!0&&(e.allowSlideNext=!e.isLocked),n.allowSlidePrev===!0&&(e.allowSlidePrev=!e.isLocked),t&&t!==e.isLocked&&(e.isEnd=!1),t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}var lf={checkOverflow:rf},Vs={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function of(e,t){return function(s){s===void 0&&(s={});const i=Object.keys(s)[0],r=s[i];if(typeof r!="object"||r===null){Ne(t,s);return}if(e[i]===!0&&(e[i]={enabled:!0}),i==="navigation"&&e[i]&&e[i].enabled&&!e[i].prevEl&&!e[i].nextEl&&(e[i].auto=!0),["pagination","scrollbar"].indexOf(i)>=0&&e[i]&&e[i].enabled&&!e[i].el&&(e[i].auto=!0),!(i in e&&"enabled"in r)){Ne(t,s);return}typeof e[i]=="object"&&!("enabled"in e[i])&&(e[i].enabled=!0),e[i]||(e[i]={enabled:!1}),Ne(t,s)}}const _s={eventsEmitter:su,update:hu,translate:wu,transition:Eu,slide:Lu,loop:Bu,grabCursor:Fu,events:Ku,breakpoints:Zu,checkOverflow:lf,classes:sf},Es={};let vi=class dt{constructor(){let t,n;for(var s=arguments.length,i=new Array(s),r=0;r<s;r++)i[r]=arguments[r];i.length===1&&i[0].constructor&&Object.prototype.toString.call(i[0]).slice(8,-1)==="Object"?n=i[0]:[t,n]=i,n||(n={}),n=Ne({},n),t&&!n.el&&(n.el=t);const l=lt();if(n.el&&typeof n.el=="string"&&l.querySelectorAll(n.el).length>1){const c=[];return l.querySelectorAll(n.el).forEach(u=>{const f=Ne({},n,{el:u});c.push(new dt(f))}),c}const o=this;o.__swiper__=!0,o.support=Bl(),o.device=kl({userAgent:n.userAgent}),o.browser=Dl(),o.eventsListeners={},o.eventsAnyListeners=[],o.modules=[...o.__modules__],n.modules&&Array.isArray(n.modules)&&o.modules.push(...n.modules);const a={};o.modules.forEach(c=>{c({params:n,swiper:o,extendParams:of(n,a),on:o.on.bind(o),once:o.once.bind(o),off:o.off.bind(o),emit:o.emit.bind(o)})});const d=Ne({},Vs,a);return o.params=Ne({},d,Es,n),o.originalParams=Ne({},o.params),o.passedParams=Ne({},n),o.params&&o.params.on&&Object.keys(o.params.on).forEach(c=>{o.on(c,o.params.on[c])}),o.params&&o.params.onAny&&o.onAny(o.params.onAny),Object.assign(o,{enabled:o.params.enabled,el:t,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return o.params.direction==="horizontal"},isVertical(){return o.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:o.params.allowSlideNext,allowSlidePrev:o.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:o.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:o.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),o.emit("_swiper"),o.params.init&&o.init(),o}getDirectionLabel(t){return this.isHorizontal()?t:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[t]}getSlideIndex(t){const{slidesEl:n,params:s}=this,i=st(n,`.${s.slideClass}, swiper-slide`),r=Gn(i[0]);return Gn(t)-r}getSlideIndexByData(t){return this.getSlideIndex(this.slides.find(n=>n.getAttribute("data-swiper-slide-index")*1===t))}getSlideIndexWhenGrid(t){return this.grid&&this.params.grid&&this.params.grid.rows>1&&(this.params.grid.fill==="column"?t=Math.floor(t/this.params.grid.rows):this.params.grid.fill==="row"&&(t=t%Math.ceil(this.slides.length/this.params.grid.rows))),t}recalcSlides(){const t=this,{slidesEl:n,params:s}=t;t.slides=st(n,`.${s.slideClass}, swiper-slide`)}enable(){const t=this;t.enabled||(t.enabled=!0,t.params.grabCursor&&t.setGrabCursor(),t.emit("enable"))}disable(){const t=this;t.enabled&&(t.enabled=!1,t.params.grabCursor&&t.unsetGrabCursor(),t.emit("disable"))}setProgress(t,n){const s=this;t=Math.min(Math.max(t,0),1);const i=s.minTranslate(),l=(s.maxTranslate()-i)*t+i;s.translateTo(l,typeof n>"u"?0:n),s.updateActiveIndex(),s.updateSlidesClasses()}emitContainerClasses(){const t=this;if(!t.params._emitClasses||!t.el)return;const n=t.el.className.split(" ").filter(s=>s.indexOf("swiper")===0||s.indexOf(t.params.containerModifierClass)===0);t.emit("_containerClasses",n.join(" "))}getSlideClasses(t){const n=this;return n.destroyed?"":t.className.split(" ").filter(s=>s.indexOf("swiper-slide")===0||s.indexOf(n.params.slideClass)===0).join(" ")}emitSlidesClasses(){const t=this;if(!t.params._emitClasses||!t.el)return;const n=[];t.slides.forEach(s=>{const i=t.getSlideClasses(s);n.push({slideEl:s,classNames:i}),t.emit("_slideClass",s,i)}),t.emit("_slideClasses",n)}slidesPerViewDynamic(t,n){t===void 0&&(t="current"),n===void 0&&(n=!1);const s=this,{params:i,slides:r,slidesGrid:l,slidesSizesGrid:o,size:a,activeIndex:d}=s;let c=1;if(typeof i.slidesPerView=="number")return i.slidesPerView;if(i.centeredSlides){let u=r[d]?Math.ceil(r[d].swiperSlideSize):0,f;for(let m=d+1;m<r.length;m+=1)r[m]&&!f&&(u+=Math.ceil(r[m].swiperSlideSize),c+=1,u>a&&(f=!0));for(let m=d-1;m>=0;m-=1)r[m]&&!f&&(u+=r[m].swiperSlideSize,c+=1,u>a&&(f=!0))}else if(t==="current")for(let u=d+1;u<r.length;u+=1)(n?l[u]+o[u]-l[d]<a:l[u]-l[d]<a)&&(c+=1);else for(let u=d-1;u>=0;u-=1)l[d]-l[u]<a&&(c+=1);return c}update(){const t=this;if(!t||t.destroyed)return;const{snapGrid:n,params:s}=t;s.breakpoints&&t.setBreakpoint(),[...t.el.querySelectorAll('[loading="lazy"]')].forEach(l=>{l.complete&&Rn(t,l)}),t.updateSize(),t.updateSlides(),t.updateProgress(),t.updateSlidesClasses();function i(){const l=t.rtlTranslate?t.translate*-1:t.translate,o=Math.min(Math.max(l,t.maxTranslate()),t.minTranslate());t.setTranslate(o),t.updateActiveIndex(),t.updateSlidesClasses()}let r;if(s.freeMode&&s.freeMode.enabled&&!s.cssMode)i(),s.autoHeight&&t.updateAutoHeight();else{if((s.slidesPerView==="auto"||s.slidesPerView>1)&&t.isEnd&&!s.centeredSlides){const l=t.virtual&&s.virtual.enabled?t.virtual.slides:t.slides;r=t.slideTo(l.length-1,0,!1,!0)}else r=t.slideTo(t.activeIndex,0,!1,!0);r||i()}s.watchOverflow&&n!==t.snapGrid&&t.checkOverflow(),t.emit("update")}changeDirection(t,n){n===void 0&&(n=!0);const s=this,i=s.params.direction;return t||(t=i==="horizontal"?"vertical":"horizontal"),t===i||t!=="horizontal"&&t!=="vertical"||(s.el.classList.remove(`${s.params.containerModifierClass}${i}`),s.el.classList.add(`${s.params.containerModifierClass}${t}`),s.emitContainerClasses(),s.params.direction=t,s.slides.forEach(r=>{t==="vertical"?r.style.width="":r.style.height=""}),s.emit("changeDirection"),n&&s.update()),s}changeLanguageDirection(t){const n=this;n.rtl&&t==="rtl"||!n.rtl&&t==="ltr"||(n.rtl=t==="rtl",n.rtlTranslate=n.params.direction==="horizontal"&&n.rtl,n.rtl?(n.el.classList.add(`${n.params.containerModifierClass}rtl`),n.el.dir="rtl"):(n.el.classList.remove(`${n.params.containerModifierClass}rtl`),n.el.dir="ltr"),n.update())}mount(t){const n=this;if(n.mounted)return!0;let s=t||n.params.el;if(typeof s=="string"&&(s=document.querySelector(s)),!s)return!1;s.swiper=n,s.parentNode&&s.parentNode.host&&s.parentNode.host.nodeName===n.params.swiperElementNodeName.toUpperCase()&&(n.isElement=!0);const i=()=>`.${(n.params.wrapperClass||"").trim().split(" ").join(".")}`;let l=s&&s.shadowRoot&&s.shadowRoot.querySelector?s.shadowRoot.querySelector(i()):st(s,i())[0];return!l&&n.params.createElements&&(l=Vn("div",n.params.wrapperClass),s.append(l),st(s,`.${n.params.slideClass}`).forEach(o=>{l.append(o)})),Object.assign(n,{el:s,wrapperEl:l,slidesEl:n.isElement&&!s.parentNode.host.slideSlots?s.parentNode.host:l,hostEl:n.isElement?s.parentNode.host:s,mounted:!0,rtl:s.dir.toLowerCase()==="rtl"||_t(s,"direction")==="rtl",rtlTranslate:n.params.direction==="horizontal"&&(s.dir.toLowerCase()==="rtl"||_t(s,"direction")==="rtl"),wrongRTL:_t(l,"display")==="-webkit-box"}),!0}init(t){const n=this;if(n.initialized||n.mount(t)===!1)return n;n.emit("beforeInit"),n.params.breakpoints&&n.setBreakpoint(),n.addClasses(),n.updateSize(),n.updateSlides(),n.params.watchOverflow&&n.checkOverflow(),n.params.grabCursor&&n.enabled&&n.setGrabCursor(),n.params.loop&&n.virtual&&n.params.virtual.enabled?n.slideTo(n.params.initialSlide+n.virtual.slidesBefore,0,n.params.runCallbacksOnInit,!1,!0):n.slideTo(n.params.initialSlide,0,n.params.runCallbacksOnInit,!1,!0),n.params.loop&&n.loopCreate(void 0,!0),n.attachEvents();const i=[...n.el.querySelectorAll('[loading="lazy"]')];return n.isElement&&i.push(...n.hostEl.querySelectorAll('[loading="lazy"]')),i.forEach(r=>{r.complete?Rn(n,r):r.addEventListener("load",l=>{Rn(n,l.target)})}),Hs(n),n.initialized=!0,Hs(n),n.emit("init"),n.emit("afterInit"),n}destroy(t,n){t===void 0&&(t=!0),n===void 0&&(n=!0);const s=this,{params:i,el:r,wrapperEl:l,slides:o}=s;return typeof s.params>"u"||s.destroyed||(s.emit("beforeDestroy"),s.initialized=!1,s.detachEvents(),i.loop&&s.loopDestroy(),n&&(s.removeClasses(),r&&typeof r!="string"&&r.removeAttribute("style"),l&&l.removeAttribute("style"),o&&o.length&&o.forEach(a=>{a.classList.remove(i.slideVisibleClass,i.slideFullyVisibleClass,i.slideActiveClass,i.slideNextClass,i.slidePrevClass),a.removeAttribute("style"),a.removeAttribute("data-swiper-slide-index")})),s.emit("destroy"),Object.keys(s.eventsListeners).forEach(a=>{s.off(a)}),t!==!1&&(s.el&&typeof s.el!="string"&&(s.el.swiper=null),Gd(s)),s.destroyed=!0),null}static extendDefaults(t){Ne(Es,t)}static get extendedDefaults(){return Es}static get defaults(){return Vs}static installModule(t){dt.prototype.__modules__||(dt.prototype.__modules__=[]);const n=dt.prototype.__modules__;typeof t=="function"&&n.indexOf(t)<0&&n.push(t)}static use(t){return Array.isArray(t)?(t.forEach(n=>dt.installModule(n)),dt):(dt.installModule(t),dt)}};Object.keys(_s).forEach(e=>{Object.keys(_s[e]).forEach(t=>{vi.prototype[t]=_s[e][t]})});vi.use([tu,nu]);const jl=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","swiperElementNodeName","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function Lt(e){return typeof e=="object"&&e!==null&&e.constructor&&Object.prototype.toString.call(e).slice(8,-1)==="Object"&&!e.__swiper__}function Yt(e,t){const n=["__proto__","constructor","prototype"];Object.keys(t).filter(s=>n.indexOf(s)<0).forEach(s=>{typeof e[s]>"u"?e[s]=t[s]:Lt(t[s])&&Lt(e[s])&&Object.keys(t[s]).length>0?t[s].__swiper__?e[s]=t[s]:Yt(e[s],t[s]):e[s]=t[s]})}function Hl(e){return e===void 0&&(e={}),e.navigation&&typeof e.navigation.nextEl>"u"&&typeof e.navigation.prevEl>"u"}function Vl(e){return e===void 0&&(e={}),e.pagination&&typeof e.pagination.el>"u"}function Gl(e){return e===void 0&&(e={}),e.scrollbar&&typeof e.scrollbar.el>"u"}function Wl(e){e===void 0&&(e="");const t=e.split(" ").map(s=>s.trim()).filter(s=>!!s),n=[];return t.forEach(s=>{n.indexOf(s)<0&&n.push(s)}),n.join(" ")}function af(e){return e===void 0&&(e=""),e?e.includes("swiper-wrapper")?e:`swiper-wrapper ${e}`:"swiper-wrapper"}function cf(e){let{swiper:t,slides:n,passedParams:s,changedParams:i,nextEl:r,prevEl:l,scrollbarEl:o,paginationEl:a}=e;const d=i.filter(O=>O!=="children"&&O!=="direction"&&O!=="wrapperClass"),{params:c,pagination:u,navigation:f,scrollbar:m,virtual:g,thumbs:S}=t;let C,b,h,y,x,E,k,H;i.includes("thumbs")&&s.thumbs&&s.thumbs.swiper&&!s.thumbs.swiper.destroyed&&c.thumbs&&(!c.thumbs.swiper||c.thumbs.swiper.destroyed)&&(C=!0),i.includes("controller")&&s.controller&&s.controller.control&&c.controller&&!c.controller.control&&(b=!0),i.includes("pagination")&&s.pagination&&(s.pagination.el||a)&&(c.pagination||c.pagination===!1)&&u&&!u.el&&(h=!0),i.includes("scrollbar")&&s.scrollbar&&(s.scrollbar.el||o)&&(c.scrollbar||c.scrollbar===!1)&&m&&!m.el&&(y=!0),i.includes("navigation")&&s.navigation&&(s.navigation.prevEl||l)&&(s.navigation.nextEl||r)&&(c.navigation||c.navigation===!1)&&f&&!f.prevEl&&!f.nextEl&&(x=!0);const z=O=>{t[O]&&(t[O].destroy(),O==="navigation"?(t.isElement&&(t[O].prevEl.remove(),t[O].nextEl.remove()),c[O].prevEl=void 0,c[O].nextEl=void 0,t[O].prevEl=void 0,t[O].nextEl=void 0):(t.isElement&&t[O].el.remove(),c[O].el=void 0,t[O].el=void 0))};i.includes("loop")&&t.isElement&&(c.loop&&!s.loop?E=!0:!c.loop&&s.loop?k=!0:H=!0),d.forEach(O=>{if(Lt(c[O])&&Lt(s[O]))Object.assign(c[O],s[O]),(O==="navigation"||O==="pagination"||O==="scrollbar")&&"enabled"in s[O]&&!s[O].enabled&&z(O);else{const A=s[O];(A===!0||A===!1)&&(O==="navigation"||O==="pagination"||O==="scrollbar")?A===!1&&z(O):c[O]=s[O]}}),d.includes("controller")&&!b&&t.controller&&t.controller.control&&c.controller&&c.controller.control&&(t.controller.control=c.controller.control),i.includes("children")&&n&&g&&c.virtual.enabled?(g.slides=n,g.update(!0)):i.includes("virtual")&&g&&c.virtual.enabled&&(n&&(g.slides=n),g.update(!0)),i.includes("children")&&n&&c.loop&&(H=!0),C&&S.init()&&S.update(!0),b&&(t.controller.control=c.controller.control),h&&(t.isElement&&(!a||typeof a=="string")&&(a=document.createElement("div"),a.classList.add("swiper-pagination"),a.part.add("pagination"),t.el.appendChild(a)),a&&(c.pagination.el=a),u.init(),u.render(),u.update()),y&&(t.isElement&&(!o||typeof o=="string")&&(o=document.createElement("div"),o.classList.add("swiper-scrollbar"),o.part.add("scrollbar"),t.el.appendChild(o)),o&&(c.scrollbar.el=o),m.init(),m.updateSize(),m.setTranslate()),x&&(t.isElement&&((!r||typeof r=="string")&&(r=document.createElement("div"),r.classList.add("swiper-button-next"),Wn(r,t.hostEl.constructor.nextButtonSvg),r.part.add("button-next"),t.el.appendChild(r)),(!l||typeof l=="string")&&(l=document.createElement("div"),l.classList.add("swiper-button-prev"),Wn(l,t.hostEl.constructor.prevButtonSvg),l.part.add("button-prev"),t.el.appendChild(l))),r&&(c.navigation.nextEl=r),l&&(c.navigation.prevEl=l),f.init(),f.update()),i.includes("allowSlideNext")&&(t.allowSlideNext=s.allowSlideNext),i.includes("allowSlidePrev")&&(t.allowSlidePrev=s.allowSlidePrev),i.includes("direction")&&t.changeDirection(s.direction,!1),(E||H)&&t.loopDestroy(),(k||H)&&t.loopCreate(),t.update()}function gr(e,t){e===void 0&&(e={});const n={on:{}},s={},i={};Yt(n,Vs),n._emitClasses=!0,n.init=!1;const r={},l=jl.map(a=>a.replace(/_/,"")),o=Object.assign({},e);return Object.keys(o).forEach(a=>{typeof e[a]>"u"||(l.indexOf(a)>=0?Lt(e[a])?(n[a]={},i[a]={},Yt(n[a],e[a]),Yt(i[a],e[a])):(n[a]=e[a],i[a]=e[a]):a.search(/on[A-Z]/)===0&&typeof e[a]=="function"?n.on[`${a[2].toLowerCase()}${a.substr(3)}`]=e[a]:r[a]=e[a])}),["navigation","pagination","scrollbar"].forEach(a=>{n[a]===!0&&(n[a]={}),n[a]===!1&&delete n[a]}),{params:n,passedParams:i,rest:r,events:s}}function df(e,t){let{el:n,nextEl:s,prevEl:i,paginationEl:r,scrollbarEl:l,swiper:o}=e;Hl(t)&&s&&i&&(o.params.navigation.nextEl=s,o.originalParams.navigation.nextEl=s,o.params.navigation.prevEl=i,o.originalParams.navigation.prevEl=i),Vl(t)&&r&&(o.params.pagination.el=r,o.originalParams.pagination.el=r),Gl(t)&&l&&(o.params.scrollbar.el=l,o.originalParams.scrollbar.el=l),o.init(n)}function uf(e,t,n,s,i){const r=[];if(!t)return r;const l=a=>{r.indexOf(a)<0&&r.push(a)};if(n&&s){const a=s.map(i),d=n.map(i);a.join("")!==d.join("")&&l("children"),s.length!==n.length&&l("children")}return jl.filter(a=>a[0]==="_").map(a=>a.replace(/_/,"")).forEach(a=>{if(a in e&&a in t)if(Lt(e[a])&&Lt(t[a])){const d=Object.keys(e[a]),c=Object.keys(t[a]);d.length!==c.length?l(a):(d.forEach(u=>{e[a][u]!==t[a][u]&&l(a)}),c.forEach(u=>{e[a][u]!==t[a][u]&&l(a)}))}else e[a]!==t[a]&&l(a)}),r}const ff=e=>{!e||e.destroyed||!e.params.virtual||e.params.virtual&&!e.params.virtual.enabled||(e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.emit("_virtualUpdated"),e.parallax&&e.params.parallax&&e.params.parallax.enabled&&e.parallax.setTranslate())};function Ts(e,t,n){e===void 0&&(e={});const s=[],i={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]},r=(l,o)=>{Array.isArray(l)&&l.forEach(a=>{const d=typeof a.type=="symbol";o==="default"&&(o="container-end"),d&&a.children?r(a.children,o):a.type&&(a.type.name==="SwiperSlide"||a.type.name==="AsyncComponentWrapper")||a.componentOptions&&a.componentOptions.tag==="SwiperSlide"?s.push(a):i[o]&&i[o].push(a)})};return Object.keys(e).forEach(l=>{if(typeof e[l]!="function")return;const o=e[l]();r(o,l)}),n.value=t.value,t.value=s,{slides:s,slots:i}}function pf(e,t,n){if(!n)return null;const s=c=>{let u=c;return c<0?u=t.length+c:u>=t.length&&(u=u-t.length),u},i=e.value.isHorizontal()?{[e.value.rtlTranslate?"right":"left"]:`${n.offset}px`}:{top:`${n.offset}px`},{from:r,to:l}=n,o=e.value.params.loop?-t.length:0,a=e.value.params.loop?t.length*2:t.length,d=[];for(let c=o;c<a;c+=1)c>=r&&c<=l&&d.length<t.length&&d.push(t[s(c)]);return d.map(c=>{if(c.props||(c.props={}),c.props.style||(c.props.style={}),c.props.swiperRef=e,c.props.style=i,c.type)return Be(c.type,{...c.props},c.children);if(c.componentOptions)return Be(c.componentOptions.Ctor,{...c.props},c.componentOptions.children)})}const hf={name:"Swiper",props:{tag:{type:String,default:"div"},wrapperTag:{type:String,default:"div"},modules:{type:Array,default:void 0},init:{type:Boolean,default:void 0},direction:{type:String,default:void 0},oneWayMovement:{type:Boolean,default:void 0},swiperElementNodeName:{type:String,default:"SWIPER-CONTAINER"},touchEventsTarget:{type:String,default:void 0},initialSlide:{type:Number,default:void 0},speed:{type:Number,default:void 0},cssMode:{type:Boolean,default:void 0},updateOnWindowResize:{type:Boolean,default:void 0},resizeObserver:{type:Boolean,default:void 0},nested:{type:Boolean,default:void 0},focusableElements:{type:String,default:void 0},width:{type:Number,default:void 0},height:{type:Number,default:void 0},preventInteractionOnTransition:{type:Boolean,default:void 0},userAgent:{type:String,default:void 0},url:{type:String,default:void 0},edgeSwipeDetection:{type:[Boolean,String],default:void 0},edgeSwipeThreshold:{type:Number,default:void 0},autoHeight:{type:Boolean,default:void 0},setWrapperSize:{type:Boolean,default:void 0},virtualTranslate:{type:Boolean,default:void 0},effect:{type:String,default:void 0},breakpoints:{type:Object,default:void 0},breakpointsBase:{type:String,default:void 0},spaceBetween:{type:[Number,String],default:void 0},slidesPerView:{type:[Number,String],default:void 0},maxBackfaceHiddenSlides:{type:Number,default:void 0},slidesPerGroup:{type:Number,default:void 0},slidesPerGroupSkip:{type:Number,default:void 0},slidesPerGroupAuto:{type:Boolean,default:void 0},centeredSlides:{type:Boolean,default:void 0},centeredSlidesBounds:{type:Boolean,default:void 0},slidesOffsetBefore:{type:Number,default:void 0},slidesOffsetAfter:{type:Number,default:void 0},normalizeSlideIndex:{type:Boolean,default:void 0},centerInsufficientSlides:{type:Boolean,default:void 0},watchOverflow:{type:Boolean,default:void 0},roundLengths:{type:Boolean,default:void 0},touchRatio:{type:Number,default:void 0},touchAngle:{type:Number,default:void 0},simulateTouch:{type:Boolean,default:void 0},shortSwipes:{type:Boolean,default:void 0},longSwipes:{type:Boolean,default:void 0},longSwipesRatio:{type:Number,default:void 0},longSwipesMs:{type:Number,default:void 0},followFinger:{type:Boolean,default:void 0},allowTouchMove:{type:Boolean,default:void 0},threshold:{type:Number,default:void 0},touchMoveStopPropagation:{type:Boolean,default:void 0},touchStartPreventDefault:{type:Boolean,default:void 0},touchStartForcePreventDefault:{type:Boolean,default:void 0},touchReleaseOnEdges:{type:Boolean,default:void 0},uniqueNavElements:{type:Boolean,default:void 0},resistance:{type:Boolean,default:void 0},resistanceRatio:{type:Number,default:void 0},watchSlidesProgress:{type:Boolean,default:void 0},grabCursor:{type:Boolean,default:void 0},preventClicks:{type:Boolean,default:void 0},preventClicksPropagation:{type:Boolean,default:void 0},slideToClickedSlide:{type:Boolean,default:void 0},loop:{type:Boolean,default:void 0},loopedSlides:{type:Number,default:void 0},loopPreventsSliding:{type:Boolean,default:void 0},loopAdditionalSlides:{type:Number,default:void 0},loopAddBlankSlides:{type:Boolean,default:void 0},rewind:{type:Boolean,default:void 0},allowSlidePrev:{type:Boolean,default:void 0},allowSlideNext:{type:Boolean,default:void 0},swipeHandler:{type:Boolean,default:void 0},noSwiping:{type:Boolean,default:void 0},noSwipingClass:{type:String,default:void 0},noSwipingSelector:{type:String,default:void 0},passiveListeners:{type:Boolean,default:void 0},containerModifierClass:{type:String,default:void 0},slideClass:{type:String,default:void 0},slideActiveClass:{type:String,default:void 0},slideVisibleClass:{type:String,default:void 0},slideFullyVisibleClass:{type:String,default:void 0},slideBlankClass:{type:String,default:void 0},slideNextClass:{type:String,default:void 0},slidePrevClass:{type:String,default:void 0},wrapperClass:{type:String,default:void 0},lazyPreloaderClass:{type:String,default:void 0},lazyPreloadPrevNext:{type:Number,default:void 0},runCallbacksOnInit:{type:Boolean,default:void 0},observer:{type:Boolean,default:void 0},observeParents:{type:Boolean,default:void 0},observeSlideChildren:{type:Boolean,default:void 0},a11y:{type:[Boolean,Object],default:void 0},autoplay:{type:[Boolean,Object],default:void 0},controller:{type:Object,default:void 0},coverflowEffect:{type:Object,default:void 0},cubeEffect:{type:Object,default:void 0},fadeEffect:{type:Object,default:void 0},flipEffect:{type:Object,default:void 0},creativeEffect:{type:Object,default:void 0},cardsEffect:{type:Object,default:void 0},hashNavigation:{type:[Boolean,Object],default:void 0},history:{type:[Boolean,Object],default:void 0},keyboard:{type:[Boolean,Object],default:void 0},mousewheel:{type:[Boolean,Object],default:void 0},navigation:{type:[Boolean,Object],default:void 0},pagination:{type:[Boolean,Object],default:void 0},parallax:{type:[Boolean,Object],default:void 0},scrollbar:{type:[Boolean,Object],default:void 0},thumbs:{type:Object,default:void 0},virtual:{type:[Boolean,Object],default:void 0},zoom:{type:[Boolean,Object],default:void 0},grid:{type:[Object],default:void 0},freeMode:{type:[Boolean,Object],default:void 0},enabled:{type:Boolean,default:void 0}},emits:["_beforeBreakpoint","_containerClasses","_slideClass","_slideClasses","_swiper","_freeModeNoMomentumRelease","_virtualUpdated","activeIndexChange","afterInit","autoplay","autoplayStart","autoplayStop","autoplayPause","autoplayResume","autoplayTimeLeft","beforeDestroy","beforeInit","beforeLoopFix","beforeResize","beforeSlideChangeStart","beforeTransitionStart","breakpoint","changeDirection","click","disable","doubleTap","doubleClick","destroy","enable","fromEdge","hashChange","hashSet","init","keyPress","lock","loopFix","momentumBounce","navigationHide","navigationShow","navigationPrev","navigationNext","observerUpdate","orientationchange","paginationHide","paginationRender","paginationShow","paginationUpdate","progress","reachBeginning","reachEnd","realIndexChange","resize","scroll","scrollbarDragEnd","scrollbarDragMove","scrollbarDragStart","setTransition","setTranslate","slidesUpdated","slideChange","slideChangeTransitionEnd","slideChangeTransitionStart","slideNextTransitionEnd","slideNextTransitionStart","slidePrevTransitionEnd","slidePrevTransitionStart","slideResetTransitionStart","slideResetTransitionEnd","sliderMove","sliderFirstMove","slidesLengthChange","slidesGridLengthChange","snapGridLengthChange","snapIndexChange","swiper","tap","toEdge","touchEnd","touchMove","touchMoveOpposite","touchStart","transitionEnd","transitionStart","unlock","update","virtualUpdate","zoomChange"],setup(e,t){let{slots:n,emit:s}=t;const{tag:i,wrapperTag:r}=e,l=pe("swiper"),o=pe(null),a=pe(!1),d=pe(!1),c=pe(null),u=pe(null),f=pe(null),m={value:[]},g={value:[]},S=pe(null),C=pe(null),b=pe(null),h=pe(null),{params:y,passedParams:x}=gr(e);Ts(n,m,g),f.value=x,g.value=m.value;const E=()=>{Ts(n,m,g),a.value=!0};y.onAny=function(z){for(var O=arguments.length,A=new Array(O>1?O-1:0),B=1;B<O;B++)A[B-1]=arguments[B];s(z,...A)},Object.assign(y.on,{_beforeBreakpoint:E,_containerClasses(z,O){l.value=O}});const k={...y};if(delete k.wrapperClass,u.value=new vi(k),u.value.virtual&&u.value.params.virtual.enabled){u.value.virtual.slides=m.value;const z={cache:!1,slides:m.value,renderExternal:O=>{o.value=O},renderExternalUpdate:!1};Yt(u.value.params.virtual,z),Yt(u.value.originalParams.virtual,z)}li(()=>{!d.value&&u.value&&(u.value.emitSlidesClasses(),d.value=!0);const{passedParams:z}=gr(e),O=uf(z,f.value,m.value,g.value,A=>A.props&&A.props.key);f.value=z,(O.length||a.value)&&u.value&&!u.value.destroyed&&cf({swiper:u.value,slides:m.value,passedParams:z,changedParams:O,nextEl:S.value,prevEl:C.value,scrollbarEl:h.value,paginationEl:b.value}),a.value=!1}),Wt("swiper",u),qt(o,()=>{ni(()=>{ff(u.value)})}),ri(()=>{c.value&&(df({el:c.value,nextEl:S.value,prevEl:C.value,paginationEl:b.value,scrollbarEl:h.value,swiper:u.value},y),s("swiper",u.value))}),oi(()=>{u.value&&!u.value.destroyed&&u.value.destroy(!0,!1)});function H(z){return y.virtual?pf(u,z,o.value):(z.forEach((O,A)=>{O.props||(O.props={}),O.props.swiperRef=u,O.props.swiperSlideIndex=A}),z)}return()=>{const{slides:z,slots:O}=Ts(n,m,g);return Be(i,{ref:c,class:Wl(l.value)},[O["container-start"],Be(r,{class:af(y.wrapperClass)},[O["wrapper-start"],H(z),O["wrapper-end"]]),Hl(e)&&[Be("div",{ref:C,class:"swiper-button-prev"}),Be("div",{ref:S,class:"swiper-button-next"})],Gl(e)&&Be("div",{ref:h,class:"swiper-scrollbar"}),Vl(e)&&Be("div",{ref:b,class:"swiper-pagination"}),O["container-end"]])}}},Cs={name:"SwiperSlide",props:{tag:{type:String,default:"div"},swiperRef:{type:Object,required:!1},swiperSlideIndex:{type:Number,default:void 0,required:!1},zoom:{type:Boolean,default:void 0,required:!1},lazy:{type:Boolean,default:!1,required:!1},virtualIndex:{type:[String,Number],default:void 0}},setup(e,t){let{slots:n}=t,s=!1;const{swiperRef:i}=e,r=pe(null),l=pe("swiper-slide"),o=pe(!1);function a(u,f,m){f===r.value&&(l.value=m)}ri(()=>{!i||!i.value||(i.value.on("_slideClass",a),s=!0)}),Kr(()=>{s||!i||!i.value||(i.value.on("_slideClass",a),s=!0)}),li(()=>{!r.value||!i||!i.value||(typeof e.swiperSlideIndex<"u"&&(r.value.swiperSlideIndex=e.swiperSlideIndex),i.value.destroyed&&l.value!=="swiper-slide"&&(l.value="swiper-slide"))}),oi(()=>{!i||!i.value||i.value.off("_slideClass",a)});const d=je(()=>({isActive:l.value.indexOf("swiper-slide-active")>=0,isVisible:l.value.indexOf("swiper-slide-visible")>=0,isPrev:l.value.indexOf("swiper-slide-prev")>=0,isNext:l.value.indexOf("swiper-slide-next")>=0}));Wt("swiperSlide",d);const c=()=>{o.value=!0};return()=>Be(e.tag,{class:Wl(`${l.value}`),ref:r,"data-swiper-slide-index":typeof e.virtualIndex>"u"&&i&&i.value&&i.value.params.loop?e.swiperSlideIndex:e.virtualIndex,onLoadCapture:c},e.zoom?Be("div",{class:"swiper-zoom-container","data-swiper-zoom":typeof e.zoom=="number"?e.zoom:void 0},[n.default&&n.default(d.value),e.lazy&&!o.value&&Be("div",{class:"swiper-lazy-preloader"})]):[n.default&&n.default(d.value),e.lazy&&!o.value&&Be("div",{class:"swiper-lazy-preloader"})])}};function ql(e,t,n,s){return e.params.createElements&&Object.keys(s).forEach(i=>{if(!n[i]&&n.auto===!0){let r=st(e.el,`.${s[i]}`)[0];r||(r=Vn("div",s[i]),r.className=s[i],e.el.append(r)),n[i]=r,t[i]=r}}),n}function mf(e){let{swiper:t,extendParams:n,on:s,emit:i}=e;n({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),t.navigation={nextEl:null,prevEl:null};function r(g){let S;return g&&typeof g=="string"&&t.isElement&&(S=t.el.querySelector(g)||t.hostEl.querySelector(g),S)?S:(g&&(typeof g=="string"&&(S=[...document.querySelectorAll(g)]),t.params.uniqueNavElements&&typeof g=="string"&&S&&S.length>1&&t.el.querySelectorAll(g).length===1?S=t.el.querySelector(g):S&&S.length===1&&(S=S[0])),g&&!S?g:S)}function l(g,S){const C=t.params.navigation;g=_e(g),g.forEach(b=>{b&&(b.classList[S?"add":"remove"](...C.disabledClass.split(" ")),b.tagName==="BUTTON"&&(b.disabled=S),t.params.watchOverflow&&t.enabled&&b.classList[t.isLocked?"add":"remove"](C.lockClass))})}function o(){const{nextEl:g,prevEl:S}=t.navigation;if(t.params.loop){l(S,!1),l(g,!1);return}l(S,t.isBeginning&&!t.params.rewind),l(g,t.isEnd&&!t.params.rewind)}function a(g){g.preventDefault(),!(t.isBeginning&&!t.params.loop&&!t.params.rewind)&&(t.slidePrev(),i("navigationPrev"))}function d(g){g.preventDefault(),!(t.isEnd&&!t.params.loop&&!t.params.rewind)&&(t.slideNext(),i("navigationNext"))}function c(){const g=t.params.navigation;if(t.params.navigation=ql(t,t.originalParams.navigation,t.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(g.nextEl||g.prevEl))return;let S=r(g.nextEl),C=r(g.prevEl);Object.assign(t.navigation,{nextEl:S,prevEl:C}),S=_e(S),C=_e(C);const b=(h,y)=>{h&&h.addEventListener("click",y==="next"?d:a),!t.enabled&&h&&h.classList.add(...g.lockClass.split(" "))};S.forEach(h=>b(h,"next")),C.forEach(h=>b(h,"prev"))}function u(){let{nextEl:g,prevEl:S}=t.navigation;g=_e(g),S=_e(S);const C=(b,h)=>{b.removeEventListener("click",h==="next"?d:a),b.classList.remove(...t.params.navigation.disabledClass.split(" "))};g.forEach(b=>C(b,"next")),S.forEach(b=>C(b,"prev"))}s("init",()=>{t.params.navigation.enabled===!1?m():(c(),o())}),s("toEdge fromEdge lock unlock",()=>{o()}),s("destroy",()=>{u()}),s("enable disable",()=>{let{nextEl:g,prevEl:S}=t.navigation;if(g=_e(g),S=_e(S),t.enabled){o();return}[...g,...S].filter(C=>!!C).forEach(C=>C.classList.add(t.params.navigation.lockClass))}),s("click",(g,S)=>{let{nextEl:C,prevEl:b}=t.navigation;C=_e(C),b=_e(b);const h=S.target;let y=b.includes(h)||C.includes(h);if(t.isElement&&!y){const x=S.path||S.composedPath&&S.composedPath();x&&(y=x.find(E=>C.includes(E)||b.includes(E)))}if(t.params.navigation.hideOnClick&&!y){if(t.pagination&&t.params.pagination&&t.params.pagination.clickable&&(t.pagination.el===h||t.pagination.el.contains(h)))return;let x;C.length?x=C[0].classList.contains(t.params.navigation.hiddenClass):b.length&&(x=b[0].classList.contains(t.params.navigation.hiddenClass)),i(x===!0?"navigationShow":"navigationHide"),[...C,...b].filter(E=>!!E).forEach(E=>E.classList.toggle(t.params.navigation.hiddenClass))}});const f=()=>{t.el.classList.remove(...t.params.navigation.navigationDisabledClass.split(" ")),c(),o()},m=()=>{t.el.classList.add(...t.params.navigation.navigationDisabledClass.split(" ")),u()};Object.assign(t.navigation,{enable:f,disable:m,update:o,init:c,destroy:u})}function sn(e){return e===void 0&&(e=""),`.${e.trim().replace(/([\.:!+\/()[\]])/g,"\\$1").replace(/ /g,".")}`}function gf(e){let{swiper:t,extendParams:n,on:s,emit:i}=e;const r="swiper-pagination";n({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:h=>h,formatFractionTotal:h=>h,bulletClass:`${r}-bullet`,bulletActiveClass:`${r}-bullet-active`,modifierClass:`${r}-`,currentClass:`${r}-current`,totalClass:`${r}-total`,hiddenClass:`${r}-hidden`,progressbarFillClass:`${r}-progressbar-fill`,progressbarOppositeClass:`${r}-progressbar-opposite`,clickableClass:`${r}-clickable`,lockClass:`${r}-lock`,horizontalClass:`${r}-horizontal`,verticalClass:`${r}-vertical`,paginationDisabledClass:`${r}-disabled`}}),t.pagination={el:null,bullets:[]};let l,o=0;function a(){return!t.params.pagination.el||!t.pagination.el||Array.isArray(t.pagination.el)&&t.pagination.el.length===0}function d(h,y){const{bulletActiveClass:x}=t.params.pagination;h&&(h=h[`${y==="prev"?"previous":"next"}ElementSibling`],h&&(h.classList.add(`${x}-${y}`),h=h[`${y==="prev"?"previous":"next"}ElementSibling`],h&&h.classList.add(`${x}-${y}-${y}`)))}function c(h,y,x){if(h=h%x,y=y%x,y===h+1)return"next";if(y===h-1)return"previous"}function u(h){const y=h.target.closest(sn(t.params.pagination.bulletClass));if(!y)return;h.preventDefault();const x=Gn(y)*t.params.slidesPerGroup;if(t.params.loop){if(t.realIndex===x)return;const E=c(t.realIndex,x,t.slides.length);E==="next"?t.slideNext():E==="previous"?t.slidePrev():t.slideToLoop(x)}else t.slideTo(x)}function f(){const h=t.rtl,y=t.params.pagination;if(a())return;let x=t.pagination.el;x=_e(x);let E,k;const H=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.slides.length,z=t.params.loop?Math.ceil(H/t.params.slidesPerGroup):t.snapGrid.length;if(t.params.loop?(k=t.previousRealIndex||0,E=t.params.slidesPerGroup>1?Math.floor(t.realIndex/t.params.slidesPerGroup):t.realIndex):typeof t.snapIndex<"u"?(E=t.snapIndex,k=t.previousSnapIndex):(k=t.previousIndex||0,E=t.activeIndex||0),y.type==="bullets"&&t.pagination.bullets&&t.pagination.bullets.length>0){const O=t.pagination.bullets;let A,B,Y;if(y.dynamicBullets&&(l=js(O[0],t.isHorizontal()?"width":"height"),x.forEach(X=>{X.style[t.isHorizontal()?"width":"height"]=`${l*(y.dynamicMainBullets+4)}px`}),y.dynamicMainBullets>1&&k!==void 0&&(o+=E-(k||0),o>y.dynamicMainBullets-1?o=y.dynamicMainBullets-1:o<0&&(o=0)),A=Math.max(E-o,0),B=A+(Math.min(O.length,y.dynamicMainBullets)-1),Y=(B+A)/2),O.forEach(X=>{const se=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(ie=>`${y.bulletActiveClass}${ie}`)].map(ie=>typeof ie=="string"&&ie.includes(" ")?ie.split(" "):ie).flat();X.classList.remove(...se)}),x.length>1)O.forEach(X=>{const se=Gn(X);se===E?X.classList.add(...y.bulletActiveClass.split(" ")):t.isElement&&X.setAttribute("part","bullet"),y.dynamicBullets&&(se>=A&&se<=B&&X.classList.add(...`${y.bulletActiveClass}-main`.split(" ")),se===A&&d(X,"prev"),se===B&&d(X,"next"))});else{const X=O[E];if(X&&X.classList.add(...y.bulletActiveClass.split(" ")),t.isElement&&O.forEach((se,ie)=>{se.setAttribute("part",ie===E?"bullet-active":"bullet")}),y.dynamicBullets){const se=O[A],ie=O[B];for(let G=A;G<=B;G+=1)O[G]&&O[G].classList.add(...`${y.bulletActiveClass}-main`.split(" "));d(se,"prev"),d(ie,"next")}}if(y.dynamicBullets){const X=Math.min(O.length,y.dynamicMainBullets+4),se=(l*X-l)/2-Y*l,ie=h?"right":"left";O.forEach(G=>{G.style[t.isHorizontal()?ie:"top"]=`${se}px`})}}x.forEach((O,A)=>{if(y.type==="fraction"&&(O.querySelectorAll(sn(y.currentClass)).forEach(B=>{B.textContent=y.formatFractionCurrent(E+1)}),O.querySelectorAll(sn(y.totalClass)).forEach(B=>{B.textContent=y.formatFractionTotal(z)})),y.type==="progressbar"){let B;y.progressbarOpposite?B=t.isHorizontal()?"vertical":"horizontal":B=t.isHorizontal()?"horizontal":"vertical";const Y=(E+1)/z;let X=1,se=1;B==="horizontal"?X=Y:se=Y,O.querySelectorAll(sn(y.progressbarFillClass)).forEach(ie=>{ie.style.transform=`translate3d(0,0,0) scaleX(${X}) scaleY(${se})`,ie.style.transitionDuration=`${t.params.speed}ms`})}y.type==="custom"&&y.renderCustom?(Wn(O,y.renderCustom(t,E+1,z)),A===0&&i("paginationRender",O)):(A===0&&i("paginationRender",O),i("paginationUpdate",O)),t.params.watchOverflow&&t.enabled&&O.classList[t.isLocked?"add":"remove"](y.lockClass)})}function m(){const h=t.params.pagination;if(a())return;const y=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.grid&&t.params.grid.rows>1?t.slides.length/Math.ceil(t.params.grid.rows):t.slides.length;let x=t.pagination.el;x=_e(x);let E="";if(h.type==="bullets"){let k=t.params.loop?Math.ceil(y/t.params.slidesPerGroup):t.snapGrid.length;t.params.freeMode&&t.params.freeMode.enabled&&k>y&&(k=y);for(let H=0;H<k;H+=1)h.renderBullet?E+=h.renderBullet.call(t,H,h.bulletClass):E+=`<${h.bulletElement} ${t.isElement?'part="bullet"':""} class="${h.bulletClass}"></${h.bulletElement}>`}h.type==="fraction"&&(h.renderFraction?E=h.renderFraction.call(t,h.currentClass,h.totalClass):E=`<span class="${h.currentClass}"></span> / <span class="${h.totalClass}"></span>`),h.type==="progressbar"&&(h.renderProgressbar?E=h.renderProgressbar.call(t,h.progressbarFillClass):E=`<span class="${h.progressbarFillClass}"></span>`),t.pagination.bullets=[],x.forEach(k=>{h.type!=="custom"&&Wn(k,E||""),h.type==="bullets"&&t.pagination.bullets.push(...k.querySelectorAll(sn(h.bulletClass)))}),h.type!=="custom"&&i("paginationRender",x[0])}function g(){t.params.pagination=ql(t,t.originalParams.pagination,t.params.pagination,{el:"swiper-pagination"});const h=t.params.pagination;if(!h.el)return;let y;typeof h.el=="string"&&t.isElement&&(y=t.el.querySelector(h.el)),!y&&typeof h.el=="string"&&(y=[...document.querySelectorAll(h.el)]),y||(y=h.el),!(!y||y.length===0)&&(t.params.uniqueNavElements&&typeof h.el=="string"&&Array.isArray(y)&&y.length>1&&(y=[...t.el.querySelectorAll(h.el)],y.length>1&&(y=y.find(x=>zl(x,".swiper")[0]===t.el))),Array.isArray(y)&&y.length===1&&(y=y[0]),Object.assign(t.pagination,{el:y}),y=_e(y),y.forEach(x=>{h.type==="bullets"&&h.clickable&&x.classList.add(...(h.clickableClass||"").split(" ")),x.classList.add(h.modifierClass+h.type),x.classList.add(t.isHorizontal()?h.horizontalClass:h.verticalClass),h.type==="bullets"&&h.dynamicBullets&&(x.classList.add(`${h.modifierClass}${h.type}-dynamic`),o=0,h.dynamicMainBullets<1&&(h.dynamicMainBullets=1)),h.type==="progressbar"&&h.progressbarOpposite&&x.classList.add(h.progressbarOppositeClass),h.clickable&&x.addEventListener("click",u),t.enabled||x.classList.add(h.lockClass)}))}function S(){const h=t.params.pagination;if(a())return;let y=t.pagination.el;y&&(y=_e(y),y.forEach(x=>{x.classList.remove(h.hiddenClass),x.classList.remove(h.modifierClass+h.type),x.classList.remove(t.isHorizontal()?h.horizontalClass:h.verticalClass),h.clickable&&(x.classList.remove(...(h.clickableClass||"").split(" ")),x.removeEventListener("click",u))})),t.pagination.bullets&&t.pagination.bullets.forEach(x=>x.classList.remove(...h.bulletActiveClass.split(" ")))}s("changeDirection",()=>{if(!t.pagination||!t.pagination.el)return;const h=t.params.pagination;let{el:y}=t.pagination;y=_e(y),y.forEach(x=>{x.classList.remove(h.horizontalClass,h.verticalClass),x.classList.add(t.isHorizontal()?h.horizontalClass:h.verticalClass)})}),s("init",()=>{t.params.pagination.enabled===!1?b():(g(),m(),f())}),s("activeIndexChange",()=>{typeof t.snapIndex>"u"&&f()}),s("snapIndexChange",()=>{f()}),s("snapGridLengthChange",()=>{m(),f()}),s("destroy",()=>{S()}),s("enable disable",()=>{let{el:h}=t.pagination;h&&(h=_e(h),h.forEach(y=>y.classList[t.enabled?"remove":"add"](t.params.pagination.lockClass)))}),s("lock unlock",()=>{f()}),s("click",(h,y)=>{const x=y.target,E=_e(t.pagination.el);if(t.params.pagination.el&&t.params.pagination.hideOnClick&&E&&E.length>0&&!x.classList.contains(t.params.pagination.bulletClass)){if(t.navigation&&(t.navigation.nextEl&&x===t.navigation.nextEl||t.navigation.prevEl&&x===t.navigation.prevEl))return;const k=E[0].classList.contains(t.params.pagination.hiddenClass);i(k===!0?"paginationShow":"paginationHide"),E.forEach(H=>H.classList.toggle(t.params.pagination.hiddenClass))}});const C=()=>{t.el.classList.remove(t.params.pagination.paginationDisabledClass);let{el:h}=t.pagination;h&&(h=_e(h),h.forEach(y=>y.classList.remove(t.params.pagination.paginationDisabledClass))),g(),m(),f()},b=()=>{t.el.classList.add(t.params.pagination.paginationDisabledClass);let{el:h}=t.pagination;h&&(h=_e(h),h.forEach(y=>y.classList.add(t.params.pagination.paginationDisabledClass))),S()};Object.assign(t.pagination,{enable:C,disable:b,render:m,update:f,init:g,destroy:S})}function vf(e){let{swiper:t,extendParams:n,on:s,emit:i,params:r}=e;t.autoplay={running:!1,paused:!1,timeLeft:0},n({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let l,o,a=r&&r.autoplay?r.autoplay.delay:3e3,d=r&&r.autoplay?r.autoplay.delay:3e3,c,u=new Date().getTime(),f,m,g,S,C,b,h;function y(D){!t||t.destroyed||!t.wrapperEl||D.target===t.wrapperEl&&(t.wrapperEl.removeEventListener("transitionend",y),!(h||D.detail&&D.detail.bySwiperTouchMove)&&A())}const x=()=>{if(t.destroyed||!t.autoplay.running)return;t.autoplay.paused?f=!0:f&&(d=c,f=!1);const D=t.autoplay.paused?c:u+d-new Date().getTime();t.autoplay.timeLeft=D,i("autoplayTimeLeft",D,D/a),o=requestAnimationFrame(()=>{x()})},E=()=>{let D;return t.virtual&&t.params.virtual.enabled?D=t.slides.find(ve=>ve.classList.contains("swiper-slide-active")):D=t.slides[t.activeIndex],D?parseInt(D.getAttribute("data-swiper-autoplay"),10):void 0},k=D=>{if(t.destroyed||!t.autoplay.running)return;cancelAnimationFrame(o),x();let ae=typeof D>"u"?t.params.autoplay.delay:D;a=t.params.autoplay.delay,d=t.params.autoplay.delay;const ve=E();!Number.isNaN(ve)&&ve>0&&typeof D>"u"&&(ae=ve,a=ve,d=ve),c=ae;const xe=t.params.speed,Se=()=>{!t||t.destroyed||(t.params.autoplay.reverseDirection?!t.isBeginning||t.params.loop||t.params.rewind?(t.slidePrev(xe,!0,!0),i("autoplay")):t.params.autoplay.stopOnLastSlide||(t.slideTo(t.slides.length-1,xe,!0,!0),i("autoplay")):!t.isEnd||t.params.loop||t.params.rewind?(t.slideNext(xe,!0,!0),i("autoplay")):t.params.autoplay.stopOnLastSlide||(t.slideTo(0,xe,!0,!0),i("autoplay")),t.params.cssMode&&(u=new Date().getTime(),requestAnimationFrame(()=>{k()})))};return ae>0?(clearTimeout(l),l=setTimeout(()=>{Se()},ae)):requestAnimationFrame(()=>{Se()}),ae},H=()=>{u=new Date().getTime(),t.autoplay.running=!0,k(),i("autoplayStart")},z=()=>{t.autoplay.running=!1,clearTimeout(l),cancelAnimationFrame(o),i("autoplayStop")},O=(D,ae)=>{if(t.destroyed||!t.autoplay.running)return;clearTimeout(l),D||(b=!0);const ve=()=>{i("autoplayPause"),t.params.autoplay.waitForTransition?t.wrapperEl.addEventListener("transitionend",y):A()};if(t.autoplay.paused=!0,ae){C&&(c=t.params.autoplay.delay),C=!1,ve();return}c=(c||t.params.autoplay.delay)-(new Date().getTime()-u),!(t.isEnd&&c<0&&!t.params.loop)&&(c<0&&(c=0),ve())},A=()=>{t.isEnd&&c<0&&!t.params.loop||t.destroyed||!t.autoplay.running||(u=new Date().getTime(),b?(b=!1,k(c)):k(),t.autoplay.paused=!1,i("autoplayResume"))},B=()=>{if(t.destroyed||!t.autoplay.running)return;const D=lt();D.visibilityState==="hidden"&&(b=!0,O(!0)),D.visibilityState==="visible"&&A()},Y=D=>{D.pointerType==="mouse"&&(b=!0,h=!0,!(t.animating||t.autoplay.paused)&&O(!0))},X=D=>{D.pointerType==="mouse"&&(h=!1,t.autoplay.paused&&A())},se=()=>{t.params.autoplay.pauseOnMouseEnter&&(t.el.addEventListener("pointerenter",Y),t.el.addEventListener("pointerleave",X))},ie=()=>{t.el&&typeof t.el!="string"&&(t.el.removeEventListener("pointerenter",Y),t.el.removeEventListener("pointerleave",X))},G=()=>{lt().addEventListener("visibilitychange",B)},Q=()=>{lt().removeEventListener("visibilitychange",B)};s("init",()=>{t.params.autoplay.enabled&&(se(),G(),H())}),s("destroy",()=>{ie(),Q(),t.autoplay.running&&z()}),s("_freeModeStaticRelease",()=>{(g||b)&&A()}),s("_freeModeNoMomentumRelease",()=>{t.params.autoplay.disableOnInteraction?z():O(!0,!0)}),s("beforeTransitionStart",(D,ae,ve)=>{t.destroyed||!t.autoplay.running||(ve||!t.params.autoplay.disableOnInteraction?O(!0,!0):z())}),s("sliderFirstMove",()=>{if(!(t.destroyed||!t.autoplay.running)){if(t.params.autoplay.disableOnInteraction){z();return}m=!0,g=!1,b=!1,S=setTimeout(()=>{b=!0,g=!0,O(!0)},200)}}),s("touchEnd",()=>{if(!(t.destroyed||!t.autoplay.running||!m)){if(clearTimeout(S),clearTimeout(l),t.params.autoplay.disableOnInteraction){g=!1,m=!1;return}g&&t.params.cssMode&&A(),g=!1,m=!1}}),s("slideChange",()=>{t.destroyed||!t.autoplay.running||(C=!0)}),Object.assign(t.autoplay,{start:H,stop:z,pause:O,resume:A})}const yf={class:"banner"},bf={class:"slide-item"},wf=["src","alt"],Sf={class:"slide-content"},xf={class:"slide-title"},_f={class:"slide-description"},Ef={__name:"BannerCarousel",setup(e){const t=[mf,gf,vf],n=pe([{id:1,image:"/images/1.jpg",title:"颗粒归仓 尽揽丰收",description:"为现代农业提效 智能农机装备整体解决方案"},{id:2,image:"/images/2.jpg",title:"智慧农业 引领未来",description:"科技赋能，打造高效、绿色的现代农业新模式"}]);return(s,i)=>(K(),ee("section",yf,[re(nt(hf),{modules:t,"slides-per-view":1,"space-between":0,navigation:"",pagination:{clickable:!0},autoplay:{delay:15e3,disableOnInteraction:!1},loop:""},{default:He(()=>[re(nt(Cs),{style:{background:"#fff"}},{default:He(()=>i[0]||(i[0]=[_("div",{class:"slide-item",style:{overflow:"hidden"}},[_("video",{src:Nd,autoplay:"",muted:"",loop:"",playsinline:"",style:{width:"100%",height:"100%","object-fit":"fill",display:"block"}})],-1)])),_:1,__:[0]}),re(nt(Cs),{style:{background:"#fff"}},{default:He(()=>i[1]||(i[1]=[_("div",{class:"slide-item",style:{overflow:"hidden"}},[_("video",{src:jd,autoplay:"",muted:"",loop:"",playsinline:"",style:{width:"100%",height:"100%","object-fit":"fill",display:"block"}})],-1)])),_:1,__:[1]}),(K(!0),ee(ue,null,ke(n.value,r=>(K(),Xt(nt(Cs),{key:r.id},{default:He(()=>[_("div",bf,[_("img",{src:r.image,alt:r.title,class:"slide-image"},null,8,wf),_("div",Sf,[_("h2",xf,be(r.title),1),_("p",_f,be(r.description),1)])])]),_:2},1024))),128))]),_:1})]))}},Tf=Ue(Ef,[["__scopeId","data-v-206f1834"]]),Cf="/images/c2.jpg",Pf="/images/c1.jpg",Mf={},Of={class:"features"};function If(e,t){return K(),ee("section",Of,t[0]||(t[0]=[ss('<div class="container" style="display:flex;justify-content:space-between;gap:8%;"><div class="card"><div><img class="card-img" src="'+Cf+'" alt="智能农业" style="border-radius:var(--border-radius);"><div class="card-content"><h3 class="title">Intelligent Agriculture<br><span>智慧农业</span></h3><p style="color:var(--text-light);font-size:1.2rem;">让农业迈向高效智能新赛道，提供农业数据监控、无人机植保、智能灌溉等整体解决方案。</p><div class="font-color-gray mt-6" style="color:#ccc;"> 围绕作物健康成长，以大数据为驱动，建成农机物联网、人工智能农机、智慧农业三个平台，并实现三个平台在中联数字农服APP中实现互联互通。 </div></div></div><div class="card-footer"><a>进一步了解 →</a></div></div><div class="card"><div><img class="card-img" src="'+Pf+'" alt="农机装备" style="border-radius:var(--border-radius);"><div class="card-content"><h3 class="title">Agricultural Machinery<br><span>农机机械</span></h3><p style="color:var(--text-light);font-size:1.2rem;">为现代农业提供高效智能农机装备，涵盖收获、播种、植保等环节。</p></div></div><div class="card-footer"><a>进一步了解 →</a></div></div></div>',1)]))}const Af=Ue(Mf,[["render",If]]),Lf={class:"news-section"},Rf={class:"container"},$f={class:"news-list"},zf={class:"news-card"},Bf=["src"],kf={class:"news-content"},Df={class:"news-title"},Ff={class:"news-desc"},Nf={class:"news-date"},jf=Pt({__name:"news-section",setup(e){const t=pe([{img:"images/s1.jpg",title:"“曲线救粮信”显身手 中联重科智能农机保障早稻优质品质",intro:"随着湖南早稻收获进度过半，粮食烘干全面进入高峰期。近日，位于常德的石公桥省级农事服务中心（以下简称“中心”）内一片繁忙：十组中联重科谷物烘干机全速运转，满载稻谷的卡车穿梭不息，工人们正高效归拢散落的湿谷。",date:"2025-07-21"},{img:"/images/s2.jpg",title:"“田间自“秀”实力，中联重科护航水稻全程机械化生产",intro:"7月11日，湖南省常德市水稻生产全程机械化“田间日”活动在汉寿县举行。中联重科多款智能农机装备精彩亮相，依次开展覆盖水稻收获、秸秆处理、耕整、插秧等生产环节的机械化作业演示，勾勒出一幅科技赋能现代农业的生动图景。",date:"2025-07-21"},{img:"/images/s3.jpg",title:"媒体聚焦丨央视关注中联重科智能农机助力水稻“双抢”",intro:"当下正值我国南方区域水稻“双抢”时节。7月10日，中央电视台财经频道《第一时间》栏目关注南方产区早稻收获进度情况，其中重点报道了中联重科智能农机设备为水稻“双抢”带来的新变化。以下是报道内容：",date:"2025-07-21"}]);return(n,s)=>(K(),ee("section",Lf,[_("div",Rf,[s[0]||(s[0]=_("div",null,[_("div",{class:"section-title"},"新闻动态"),_("div",{class:"text-center"},"汇聚公司咨询，了解行业动态"),_("div",{class:"block-line"})],-1)),_("div",$f,[(K(!0),ee(ue,null,ke(t.value,(i,r)=>(K(),ee("div",zf,[_("img",{src:i.img,alt:"新闻",class:"news-img"},null,8,Bf),_("div",kf,[_("h4",Df,be(i.title),1),_("p",Ff,be(i.intro),1),_("p",Nf,be(i.date),1)])]))),256))])])]))}}),Hf=Ue(jf,[["__scopeId","data-v-9ac2bac2"]]),Vf={class:"about",style:{padding:"48px 0"}},Gf={class:"container flex",style:{gap:"32px","flex-wrap":"wrap","align-items":"flex-start"}},Wf={style:{flex:"1 1 320px","min-width":"260px"}},qf={__name:"Index",setup(e){return(t,n)=>(K(),ee("main",null,[re(Tf),re(Af),re(Hf),_("section",Vf,[_("div",Gf,[_("div",Wf,[n[1]||(n[1]=_("div",{class:"mb-10"},[_("h2",{class:"section-title",style:{"text-align":"left"}},"关于我们"),_("div",{class:"block-line mb-5",style:{"margin-left":"0",width:"80px"}})],-1)),n[2]||(n[2]=_("p",{style:{"font-size":"1rem","line-height":"2",color:"var(--text-light)","margin-bottom":"1.5em"}},"辽宁紫竹农装有限公司专注于农机装备、智慧农业、现代农业解决方案。公司拥有雄厚的技术研发实力和完善的服务体系，致力于推动中国农业现代化进程。公司产品涵盖智能农机、农业物联网、无人机植保等领域，服务全国各地农业生产。",-1)),re(Fd,null,{default:He(()=>n[0]||(n[0]=[Ut("进一步了解")])),_:1,__:[0]})]),n[3]||(n[3]=_("div",{style:{flex:"1 1 320px","min-width":"260px","margin-top":"-90px"},class:""},[_("img",{class:"shadow",src:zd,alt:"公司大楼",style:{width:"100%","border-radius":"var(--border-radius)"}})],-1))])])]))}},Uf=Ue(qf,[["__scopeId","data-v-34fe0ffd"]]),Yf="/images/b1.jpg",Xf={class:"category-menu"},Kf=["onClick"],Qf=["onClick"],Jf=Pt({__name:"ProductNav",emits:["update:categoryObj"],setup(e,{emit:t}){const n=t,s=pe(0),i=pe(0),r=pe(fi);return qt(()=>[s.value,i.value],()=>{n("update:categoryObj",r.value[s.value])},{immediate:!0}),(l,o)=>(K(),ee("aside",Xf,[(K(!0),ee(ue,null,ke(r.value,(a,d)=>(K(),ee("div",{key:a.name,onClick:c=>s.value=d},[_("div",{class:Tt(["category-item",{active:d===s.value}])},be(a.name),3),(K(!0),ee(ue,null,ke(a.children,(c,u)=>(K(),ee("div",{class:Tt(["prod-item",{active:u==i.value&&d===s.value}]),onClick:f=>{i.value=u,s.value=d}},be(c.name),11,Qf))),256))],8,Kf))),128))]))}}),Zf={class:"product-center"},ep={class:"container main-content"},tp={class:"product-list"},np={class:"product-group"},sp={class:"group-title"},ip={class:"group-products"},rp=["src","alt"],lp={class:"product-info"},op={class:"product-name"},ap={class:"product-params"},cp={class:"param-key"},dp={class:"param-val"},up=Pt({__name:"Products",setup(e){const t=pe({name:"",children:[]});return(n,s)=>{const i=xn("router-link");return K(),ee("div",Zf,[s[3]||(s[3]=_("div",{class:"banner"},[_("img",{class:"banner-img",src:Yf,alt:"产品中心横幅"}),_("div",{class:"banner-content"},[_("h1",null,"产品中心"),_("div",{class:"banner-desc"},"以人为本 细节至上")])],-1)),_("div",ep,[re(Jf,{categoryObj:t.value,"onUpdate:categoryObj":s[0]||(s[0]=r=>t.value=r)},null,8,["categoryObj"]),_("section",tp,[_("div",np,[_("div",sp,be(t.value.name),1),_("div",ip,[(K(!0),ee(ue,null,ke(t.value.children,r=>(K(),Xt(i,{to:"/product/detail"},{default:He(()=>[(K(),ee("div",{key:r.name,class:"product-card"},[_("img",{src:r.img,class:"product-img",alt:r.name},null,8,rp),_("div",lp,[_("div",op,be(r.name),1),_("div",ap,[(K(!0),ee(ue,null,ke(r.params,(l,o)=>(K(),ee("div",{key:o,class:"product-param"},[_("div",cp,be(o)+"：",1),_("div",dp,be(l),1)]))),128)),s[1]||(s[1]=_("div",{class:"product-param"},[_("div",{class:"param-key"},"尺寸："),_("div",{class:"param-val"},"163CM * 200CM")],-1)),s[2]||(s[2]=_("div",{class:"product-param"},[_("div",{class:"param-key"},"参数2："),_("div",{class:"param-val"},"163CM * 200CM")],-1))])])]))]),_:2},1024))),256))])])])])])}}}),fp=Ue(up,[["__scopeId","data-v-0b752f2a"]]),pp="/images/pp1_1.png",hp="/images/pp11.jpg",mp="/images/pp1_4.png",gp="/images/pp1_3.png",vp="/images/pp1_2.png",yp={style:{background:"#6cb62a"}},bp={class:"container flex-between",style:{height:"48px",background:"#6cb62a"}},wp={class:"flex-center",style:{width:"100%"}},Sp={class:"tab-item active"},xp={class:"tab-item"},_p={class:"tab-item"},Ep=Pt({__name:"Detail",setup(e){function t(n){var s;(s=document.querySelector("a[name="+n+"]"))==null||s.scrollIntoView({behavior:"smooth"})}return(n,s)=>(K(),ee("div",null,[s[3]||(s[3]=_("div",{class:"banner-container",style:{"background-image":"url('/images/pp1.jpg')","background-repeat":"no-repeat","background-size":"auto","background-position":"center"}},null,-1)),_("div",yp,[_("div",bp,[_("div",wp,[_("div",Sp,[_("a",{onClick:s[0]||(s[0]=i=>t("js"))},"产品介绍")]),_("div",xp,[_("a",{onClick:s[1]||(s[1]=i=>t("td"))},"产品特点")]),_("div",_p,[_("a",{onClick:s[2]||(s[2]=i=>t("cs"))},"产品参数")])])])]),s[4]||(s[4]=ss('<div style="background-color:#fff;" data-v-88ead44c><section class="container" style="padding:48px;" data-v-88ead44c><a name="js" data-v-88ead44c></a><div class="flex-around" style="margin-bottom:32px;" data-v-88ead44c><img src="'+pp+'" alt="产品特点" style="object-fit:cover;border-radius:8px;box-shadow:0 2px 8px #0001;" data-v-88ead44c></div><a name="td" data-v-88ead44c></a><div class="section-title" style="" data-v-88ead44c> 产品特点 </div><div data-v-88ead44c><img src="'+hp+'" alt="产品特点" style="object-fit:cover;border-radius:8px;" data-v-88ead44c></div><a name="cs" data-v-88ead44c></a><div class="section-title" style="" data-v-88ead44c> 产品参数 </div><div class="flex-around" style="margin-bottom:32px;" data-v-88ead44c><img src="'+mp+'" alt="产品参数" style="object-fit:cover;border-radius:8px;box-shadow:0 2px 8px #0001;" data-v-88ead44c></div><div class="flex-around" style="margin-bottom:32px;" data-v-88ead44c><img src="'+gp+'" alt="产品图片" style="width:320px;height:200px;object-fit:contain;border-radius:8px;box-shadow:0 2px 8px #0001;" data-v-88ead44c><img src="'+vp+'" alt="产品图片" style="width:320px;height:200px;object-fit:contain;border-radius:8px;box-shadow:0 2px 8px #0001;" data-v-88ead44c></div></section><section style="bpadding:48px 0;" data-v-88ead44c><div class="container" style="text-align:center;" data-v-88ead44c><img src="" alt="产品视频" data-v-88ead44c></div></section></div>',1))]))}}),Tp=Ue(Ep,[["__scopeId","data-v-88ead44c"]]),Cp="/images/b2.jpg",Pp="/images/ss1.jpg",Mp="/images/ss2.jpg",Op="/images/ss3.jpg",Ip="/images/ss_m.jpg",Ap="/images/ss_p3.jpg",Lp="/images/ss_p2.jpg",Rp="/images/ss_p1.jpg",$p={class:"main-content"},zp={class:"flex-center",style:{gap:"160px"}},Bp=Pt({__name:"Support",setup(e){return(t,n)=>{const s=xn("router-link");return K(),ee("div",null,[n[3]||(n[3]=_("div",{class:"banner-container"},[_("img",{class:"banner-img",src:Cp,alt:"产品中心横幅"}),_("div",{class:"banner-content"},[_("h1",null,"欢迎使用服务与支持"),_("div",{class:"banner-desc"},"秉承“直通到田间，我在你身边”的服务理念，坚持“响应快、到达快、处置快”的原则，依托分布在全国各地的服务网点，物联网平台信息技术手段，实现人机互联、远程诊断、一键报修等服务，为客户保驾护航。")])],-1)),_("div",$p,[_("section",zp,[n[1]||(n[1]=_("img",{src:Pp,alt:"",srcset:""},null,-1)),re(s,{to:"/support/repair"},{default:He(()=>n[0]||(n[0]=[_("img",{src:Mp,alt:"",srcset:"",style:{height:"100%",width:"100%"}},null,-1)])),_:1,__:[0]}),n[2]||(n[2]=_("img",{src:Op,alt:"",srcset:""},null,-1))])]),n[4]||(n[4]=ss('<section class="flex" style="background-color:var(--bg-color);" data-v-77c0a507><div class="" style="width:50%;" data-v-77c0a507><img src="'+Ip+'" style="height:100%;width:100%;" data-v-77c0a507></div><div class="flex flex-col" style="align-items:flex-start;gap:30px;width:50%;padding-left:10%;" data-v-77c0a507><div class="flex" data-v-77c0a507><img src="'+Ap+'" alt="" srcset="" data-v-77c0a507><div data-v-77c0a507> 为用户提供全面的 <br data-v-77c0a507> 技术相关的相关操作指引 </div></div><div class="flex" data-v-77c0a507><img src="'+Lp+'" alt="" srcset="" data-v-77c0a507><div data-v-77c0a507> 支付相关问题，物流及配送 <br data-v-77c0a507> 售后技术支持问题，安装服务，其他产品咨询 </div></div><div class="flex" data-v-77c0a507><img src="'+Rp+'" alt="" srcset="" data-v-77c0a507><div data-v-77c0a507> 电话销售网点查询，服务网点查询 <br data-v-77c0a507> 自提服务 </div></div></div></section><div class="main-content" data-v-77c0a507></div>',2))])}}}),kp=Ue(Bp,[["__scopeId","data-v-77c0a507"]]),Dp="/images/b4.jpg",Fp={class:"product-center"},Np={class:"container main-content"},jp={class:"category-menu"},Hp=["onClick"],Vp=["onClick"],Gp={class:"product-list"},Wp={class:"product-group"},qp={class:"group-title"},Up={class:"group-products"},Yp={class:"product-info"},Xp={class:"product-name"},Kp=Pt({__name:"Repair",setup(e){const t=pe(0),n=pe(0),i=pe([{category:"使用手删",children:[{name:"打捆机",items:[{name:"9YFQ-2.23方草捆打捆机"},{name:"9YFW-2.2秸秆饲料打捆机"},{name:"9YG-2.25圆捆机"}]},{name:"集捆机",items:[{name:"方草捆集捆机"}]},{name:"集垛机",items:[{name:"方草捆集捆机"}]}]},{category:"维修教程",children:[{name:"综合故障问题",items:[{name:"转向失效"},{name:"变速箱、后桥总成损坏"},{name:"制动失效"},{name:"捣缸、冲缸、飞轮炸裂"},{name:"变速箱内的轴断"},{name:"拖拉机作业中断、停驶或性能下降"},{name:"金属表面锈蚀、零件表面强度降低"}]},{name:"油压系统故障",items:[{name:"油泵打不起油或输油量过低及压力不足 "},{name:"泵杆与泵杆、 泵杆与衬套之间的间隙过大， 磨损严重 "},{name:"油泵打不起油或输油量过低及压力不足 "},{name:" 回油箱内透气性太差， 油中混气过多 "},{name:"油液粘度太大或油温太高 "},{name:"油泵轴向间隙或径向间隙过大 "},{name:"延时减载阀不能关闭或安全阀开启 "}]},{name:"机电控制系统故障",items:[{name:"路老化短路、控制系统电器部件过载"},{name:"机械振动明显"},{name:"自身散热性能不稳定"},{name:"环路接口接触不良"}]}]},{category:"保养教程",children:[{name:"",items:[{name:"预防沙、尘等物进入机械的摩擦表面"},{name:"经常保持空气、燃油、机油等过滤装置的良好过滤性能"},{name:"清除粘附在机械外部的泥土杂物，减少磨损的因素"},{name:"及时检查、调整各主要机构"},{name:"要向修理点索要维修凭证和所更换零件的发票"},{name:"合乎规格的燃油和润滑油"}]}]}]);return(r,l)=>{var a;const o=xn("router-link");return K(),ee("div",Fp,[l[2]||(l[2]=_("div",{class:"banner"},[_("img",{class:"banner-img",src:Dp,alt:"产品中心横幅"}),_("div",{class:"banner-content"},[_("h1",null,"产品中心"),_("div",{class:"banner-desc"},"以人为本 细节至上")])],-1)),_("div",Np,[_("aside",jp,[(K(!0),ee(ue,null,ke(i.value,(d,c)=>(K(),ee("div",{key:d.category,onClick:u=>t.value=c},[_("div",{class:Tt(["category-item",{active:c===t.value}])},be(d.category),3),(K(!0),ee(ue,null,ke(d.children,(u,f)=>(K(),ee("div",{class:Tt(["prod-item",{active:f==n.value&&c===t.value}]),onClick:m=>{n.value=f,t.value=c}},be(u.name),11,Vp))),256))],8,Hp))),128))]),_("section",Gp,[_("div",Wp,[_("div",qp,be(i.value[t.value].category)+" - "+be((a=i.value[t.value].children[n.value])==null?void 0:a.name),1),_("div",Up,[(K(!0),ee(ue,null,ke((i.value[t.value].children[n.value]||i.value[t.value].children[0]).items,d=>(K(),Xt(o,{to:"#",key:d.name,class:"item-card"},{default:He(()=>[_("div",Yp,[_("div",Xp,be(d.name),1),re(o,{to:"#"},{default:He(()=>l[0]||(l[0]=[Ut("查看详情 >")])),_:1,__:[0]})])]),_:2},1024))),128))])]),l[1]||(l[1]=_("div",{class:"paginer"},[_("button",null,"上一页"),_("button",{class:"active"},"1"),_("button",null,"2"),_("button",null,"3"),_("span",null,"..."),_("button",null,"10"),_("button",null,"下一页")],-1))])])])}}}),Qp=Ue(Kp,[["__scopeId","data-v-9ba63da9"]]),Jp="/images/b31.jpg",Zp="/images/b312.jpg",eh="/images/b321.jpg",th="/images/b322.jpg",nh={__name:"About",setup(e){return(t,n)=>(K(),ee("div",null,n[0]||(n[0]=[ss('<div class="banner-container" style="background-image:url(&#39;/images/b3.jpg&#39;);background-size:cover;background-position:center;" data-v-e85ea312><div class="container" style="height:320px;display:flex;flex-direction:column;justify-content:center;" data-v-e85ea312><h2 style="color:#fff;font-size:2.2rem;font-weight:bold;margin-bottom:12px;" data-v-e85ea312>关于我们</h2><p style="color:#fff;font-size:1.2rem;" data-v-e85ea312>品质打造品牌，服务提高荣誉</p></div></div><section class="container" style="padding:48px 0 32px 0;" data-v-e85ea312><h3 class="section-title mb-5" data-v-e85ea312>公司简介</h3><p style="text-align:center;max-width:800px;margin:0 auto 32px auto;color:var(--text-light);line-height:1.6;letter-spacing:2px;" data-v-e85ea312> 辽宁紫竹集团成立于2008年，是从事钢铁冶炼、轧钢生产、钢材深加工、高端装备制造、国际进出口贸易、高新技术研发等业务的综合性集团企业。现旗下有：鞍山紫竹第三轧钢有限公司、鞍山紫竹科技型钢有限公司、鞍山紫竹重型特钢有限公司、鞍山紫竹轻型特钢有限公司、辽宁紫竹智慧农业装备有限公司、辽宁紫竹高科装备股份有限公司、鞍山紫竹国际贸易有限公司、鞍山紫竹物资有限公司、辽宁紫竹高新技术设计研发有限公司等十余家大中型企业。在哈尔滨、上海、广州、武汉等地成立8家分公司，并在美国、荷兰、澳大利亚、南非、香港等海外国家和地区设立5家全资子公司。 </p><div class="flex-around" style="margin:64px 0;" data-v-e85ea312><div class="text-center" data-v-e85ea312><div class="f-big" data-v-e85ea312>10 <span class="f-sm" data-v-e85ea312>年</span></div><div class="f-sm" data-v-e85ea312>运营经验<br data-v-e85ea312><span class="f-min font-color-gray" data-v-e85ea312>Operational experience</span></div></div><div class="text-center" data-v-e85ea312><div class="f-big" data-v-e85ea312>4 <span class="f-sm" data-v-e85ea312>年</span></div><div class="f-sm" data-v-e85ea312>行业沉淀<br data-v-e85ea312><span class="f-min font-color-gray" data-v-e85ea312>Industry precipitation</span></div></div><div class="text-center" data-v-e85ea312><div class="f-big" data-v-e85ea312>8 <span class="f-sm" data-v-e85ea312>项</span></div><div class="f-sm" data-v-e85ea312>国家专利<br data-v-e85ea312><span class="f-min font-color-gray" data-v-e85ea312>National patent</span></div></div><div class="text-center" data-v-e85ea312><div class="f-big" data-v-e85ea312>20 <span class="f-sm" data-v-e85ea312>万</span></div><div class="f-sm" data-v-e85ea312>用户积累<br data-v-e85ea312><span class="f-min font-color-gray" data-v-e85ea312>User accumulation</span></div></div></div></section><div data-v-e85ea312><img src="'+Jp+'" style="width:100%;" data-v-e85ea312></div><section class="container" style="margin-bottom:48px;" data-v-e85ea312><div class="flex-between" style="background:#fff;position:relative;margin-top:-48px;box-shadow:0 2px 8px #0001;overflow:hidden;align-items:stretch;" data-v-e85ea312><div style="flex:75%;padding:64px;" data-v-e85ea312><h4 style="font-size:1.2rem;font-weight:bold;" data-v-e85ea312>关于我们</h4><div class="font-color-gray" data-v-e85ea312>About Us</div><p class="font-color-gray" style="margin:16px 0;text-indent:32px;" data-v-e85ea312> 公司现有员工500人，其中平均学历本科及以上的有160人，中、高级技术人员80人，中、高级工人技术人员260人。公司拥有多项自主知识产权和专利技术，产品远销国内外，深受客户好评。集团产品主要围绕钢轨、钢板桩、特高压电力铁塔角钢、桥梁用热轧U型肋、船用热轧型钢及各类异型钢材的生产，以及多功能静压植桩机、液压冲击锤、液压振动锤、地下连续墙成槽机、S-TRD工法机、方草捆捡拾码垛机等高端设备的研发与制造。 </p><p class="font-color-gray" style="margin:16px 0;text-indent:32px;" data-v-e85ea312> 公司拥有多项自主知识产权和专利技术，产品远销国内外，深受客户好评。集团产品主要围绕钢轨、钢板桩、特高压电力铁塔角钢、桥梁用热轧U型肋、船用热轧型钢及各类异型钢材的生产，以及多功能静压植桩机、液压冲击锤、液压振动锤、地下连续墙成槽机、S-TRD工法机、方草捆捡拾码垛机等高端设备的研发与制造。 </p></div><div style="flex:25%;background:#72b11c;color:#fff;display:flex;flex-direction:column;justify-content:space-between;align-items:flex-start;min-width:200px;" data-v-e85ea312><div style="padding:48px;" data-v-e85ea312><div style="font-size:1.1rem;margin-bottom:8px;" data-v-e85ea312>加入我们</div><div style="font-size:2rem;margin-bottom:16px;" data-v-e85ea312>JOIN US</div><div style="border-top:4px solid #fff;width:30px;" data-v-e85ea312></div></div><div style="border-top:1px solid #f0f0f055;width:100%;padding:24px;" class="flex-between" data-v-e85ea312><img src="'+Zp+'" alt="" srcset="" data-v-e85ea312><a href="#" style="color:#fff;margin-top:12px;text-decoration:underline;" data-v-e85ea312>查看更多</a></div></div></div></section><section style="position:relative;background:#222;" data-v-e85ea312><img src="'+eh+'" alt="企业理念" style="width:100%;height:600px;object-fit:cover;opacity:0.7;" data-v-e85ea312><div style="position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:column;justify-content:center;align-items:flex-start;padding-left:10vw;" data-v-e85ea312><h3 style="color:#fff;font-size:2rem;font-weight:bold;margin-bottom:16px;font-style:italic;position:absolute;right:10%;left:10%;top:10%;" data-v-e85ea312>企业理念</h3><div style="color:#fff;font-size:1.1rem;line-height:3;position:absolute;left:40%;top:20%;" data-v-e85ea312><div data-v-e85ea312>企业精神：自强不息，产业报国</div><div data-v-e85ea312>企业愿景：创建一流企业，造就一流人才，做出一流贡献</div><div data-v-e85ea312>企业价值观：公正自强，心存感激</div><div data-v-e85ea312>管理理念：以人为本，细节制胜</div><div data-v-e85ea312>经营理念：以规模效发展，以提要效益，以创新求生存</div><div data-v-e85ea312>服务理念：服务至上，满意为本</div><div data-v-e85ea312>培训理念：让员工更出色，让企业更卓越</div></div></div></section><section class="container" style="margin:48px auto;" data-v-e85ea312><div style="width:100%;max-width:800px;margin:0 auto;" data-v-e85ea312><img src="'+th+'" alt="公司地图" style="width:100%;border:2px solid #ddd;" data-v-e85ea312></div></section>',6)])))}},sh=Ue(nh,[["__scopeId","data-v-e85ea312"]]),ih=[{path:"/",component:Uf},{path:"/product",component:fp},{path:"/product/detail",component:Tp},{path:"/support",component:kp},{path:"/support/repair",component:Qp},{path:"/about",component:sh}],rh=fd({history:Mc(),routes:ih});oc($d).use(rh).mount("#app");
