<template>
  <footer class="footer">
    <div class="container grid" style="grid-template-columns:repeat(auto-fit,minmax(160px,1fr));gap:32px;align-items:flex-start;">
      <div>
        <img src="@/assets/images/logo-p8.png" alt="logo" style="height:40px;">
        <div style="margin:1em 0 0.5em 0;font-weight:bold;">紫竹农装</div>
        <div class="font-color-gray f-sm">专注智能农机与智慧农业</div>
      </div>
      <div v-for="section in footerLinks" :key="section.title">
        <div style="font-weight:bold;margin-bottom:0.5em;">{{ section.title }}</div>
        <ul style="font-size:0.8rem;line-height:2;">
          <li v-for="item in section.items" :key="item.text"><a :href="item.link">{{ item.text }}</a></li>
        </ul>
      </div>
    </div>
    <div style="text-align:center;margin-top:2em;font-size:0.95rem;opacity:0.7;">© 2025 紫竹农装 版权所有</div>
  </footer>
</template>

<script setup>
import { ref } from 'vue';
import products from '../assets/products';
const footerLinks = ref([
  {
    title: '产品中心',
    items: products.map(v=>({text:v.name,link:'/product'}))
  },
  {
    title: '服务支持',
    items: [
      { text: '服务网站', link: '#' },
      { text: '维修保养', link: '#' },
    ]
  },
  {
    title: '新闻中心',
  },
  {
    title: '关于我们',
    items: [
      { text: '公司介绍', link: '#' },
      // { text: '联系我们', link: '#' },
      { text: '加入我们', link: '#' },
    ]
  }
]);
</script>

<style scoped>
.footer {
  background: var(--footer-bg);
  color: var(--footer-text);
  padding: 32px 0;
}
.footer a:hover {
  color: var(--primary-color);
  transition: color var(--transition);
}
</style> 