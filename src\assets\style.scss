
:root {
  --primary-color: #dfb330;
  --secondary-color: #f5f5f5;
  --accent-color: #f9b233;
  --text-color: #4e4e4e;
  --text-light: #888;
  --bg-color: #f6f6f6;
  --border-radius: 0px;
  --border-color: #e2e2e2;
  --transition: 0.3s;
  --max-width: 1300px;
  --header-height:90px;
  --header-top-height:30px;
  --footer-bg: #222;
  --footer-text: #eee;
  --font-size-big: 1.4rem;
}
html{
  font-size: 16px;
  font-weight:300;
}
body {
  background: var(--bg-color);
  color: var(--text-color);
  font-family: 'Microsoft YaHei','HarmonyOS Sans', 'PingFang SC', 'Source Han Sans SC',  'Segoe UI', 'Arial Rounded MT Bold', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
a{

}
.banner-container{
  position: relative;
  width: 100%;
  height: 510px;
  background: #eee;
  overflow: hidden;
}
.container {
  max-width: var(--max-width);
  margin: 0 auto;
  padding: 0 16px;
}

.flex {
  display: flex;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.grid {
  display: grid;
  gap: 24px;
}

.btn {
  display: inline-block;
  padding: 0.5em 2.5em;
  background: var(--primary-color);
  color: #fff;
  font-size: 1rem;
  transition: all var(--transition);
  cursor: pointer;
  
  &:hover {
    background: var(--accent-color);
    color: #fff;
  }
  &:active{
    transform: translate(3px,3px);
  }
  &.null{
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color) !important;
  }
}
.flex{
  display: flex;
}
.flex-center{
  display: flex; 
  justify-content: center;
  align-items: center;
}
.flex-around{
  display: flex; 
  justify-content: space-around;
  align-items: center;
}
.flex-between{
  display: flex; 
  justify-content: space-between;
  align-items: center;
}
.section-title {
  font-size: 2rem;
  font-weight: bold;
  font-style: italic;
}
.section-content{
  font-size:1rem;line-height:2;color:var(--text-light);margin-bottom:1.5em;
  color: #333;
}

.f-sm{
  font-size: 0.9rem;
}
.f-min{
  font-size: 0.8rem;
}
.f-i{
  font-style: italic;
}
.mp{
  margin: 0.5em;
}

.font-color-gray{
  color: var(--text-light);
}

.text-center {
  text-align: center;
}

@media (max-width: 900px) {
  .container {
    padding: 0 8px;
  }
  .section-title {
    font-size: 1.5rem;
  }
}

@media (max-width: 600px) {
  :root {
    --header-height: 56px;
    --max-width: 100vw;
    --border-radius: 6px;
  }
  .card {
    padding: 12px;
  }
  .section-title {
    font-size: 1.2rem;
  }
}
.block-line{
  background: var(--primary-color);
  width: 30px;
  height: 5px;
  margin: 10px auto 30px auto;
}

@media (max-width: 900px) {
  .main-content {
    flex-direction: column;
    padding: 16px;
    gap: 16px;
  }
  .category-menu {
    flex-direction: row;
    width: 100%;
    padding: 8px 0;
    gap: 0;
    overflow-x: auto;
  }
  .category-item {
    border-radius: 4px 4px 0 0;
    padding: 10px 16px;
    font-size: 1rem;
  }
}


.category-menu {
  width: 300px;
  background: #fafafa;
  border-radius: 8px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 8px;

  .prod-item {
      margin: 10px 0px;
      padding-left: 1rem;
      font-size: .9em;
      line-height: 2.5;
      cursor: pointer;
      &:hover,
      &.active {
          background-color: #f5efd4;
          color: var(--primary-color);
      }
  }
}

.category-item {
  padding: 8px 24px;
  cursor: pointer;
  border-radius: 2px;
  font-weight: 600;
  transition: background 0.2s, color 0.2s;
  background: #eee;

}

.category-item.active,
.category-item:hover {
  background: var(--primary-color);
  color: #fff;
  font-weight: bold;
}

.banner {
  position: relative;
  width: 100%;
  height: 510px;
  background: #eee;
  overflow: hidden;
}
.banner-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.banner-content {
  position: absolute;
  left: 15%;
  top: 30%;
  color: #fff;
  text-shadow: 0 2px 8px rgba(0,0,0,0.4);
}
.banner-content h1 {
  font-size: 2.5rem;
  font-weight: bold;
}
.banner-desc {
  font-size: 1.3rem;
  margin-top: 1em;
}

/* 分页组件样式 */
.paginer {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 24px;
}

.paginer button {
  padding: 6px 12px;
  border: 1px solid #ccc;
  background: #fff;
  border-radius: 4px;
  cursor: pointer;
  color: #333;
  transition: background 0.2s, color 0.2s, border 0.2s;
}

.paginer button.active,
.paginer button.selected,
.paginer button[aria-current="page"] {
  border: 1px solid var(--primary-color);
  background: var(--primary-color);
  color: #fff;
}

.paginer button:disabled {
  color: #bbb;
  border-color: #eee;
  background: #f5f5f5;
  cursor: not-allowed;
}

.paginer button:not(:disabled):hover {
  border: 1px solid var(--primary-color);
  background: var(--primary-color);
  color: #fff;
}

.paginer span {
  padding: 0 8px;
  color: #888;
}

.main-m{
  display: flex;
  margin: -30px auto auto auto;
  position: relative;
  z-index: 1;
  background-color: #fff;
  box-shadow: 0 0 20px #999;
  
  &>*{
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    gap: 10px;
    padding:15px 20px;
    cursor: pointer;
    border-right:1px solid #ccc;
    border-bottom:1px solid #ccc;
    
    &.active,&:hover{
      background-color: var(--primary-color);
      color: #fff;
      .icon{
        background-color: #fff;
        img{
          filter: invert(1);
        }
      }
    }
    .icon{
      background-color: var(--primary-color);
      border-radius: 50%;
      padding: 8px;
    }
    img{
      width: 18px;
      height:18px;
      filter: brightness(1000%);
    }
  }
}