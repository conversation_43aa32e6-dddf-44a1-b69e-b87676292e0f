<template>
  <div style="background-color: #fff;">
    <!-- 顶部横幅 -->
    <div class="banner-container">
      <img class="banner-img" src="/images/b2.jpg" alt="产品中心横幅" />
      <div class="banner-content" style="margin-left: -25%;">
        <h1>欢迎使用服务与支持</h1>
        <div class="banner-desc">秉承“直通到田间，我在你身边”的服务理念，坚持“响应快、到达快、处置快”的原则，依托分布在全国各地的服务网点，物联网平台信息技术手段，实现人机互联、远程诊断、一键报修等服务，为客户保驾护航。</div>
      </div>
    </div>
    <div class="container" style="padding:60px 80px;">
      <section class="flex-around " style="gap: 50px;">
          <div class="supper-item flex-center flex-col gap-2" style="width: 18%;" @click="router.push('/support/repair')">
            <img src="/images/ss_p1.jpg" alt="" srcset="">

              <div  class="text-lg font-medium">维修保养</div>
              <div class="f-sm text-center">
                查询服务网点就近送修或现场咨询，
获得检测、维修等服务支持。
              </div>
            </div>
                      <div class="supper-item flex-center flex-col gap-2" style="width: 18%;">
            <img src="/images/ss_p2.jpg" alt="" srcset="">

              <div class="text-lg font-medium">服务网点</div>
              <div class="f-sm text-center">
                查询服务网点就近送修或现场咨询，
获得检测、维修等服务支持。
              </div>
            </div>
                      <div class=" supper-item flex-center flex-col gap-2" style="width: 18%;">
            <img src="/images/ss_p3.jpg" alt="" srcset="">

              <div class="text-lg font-medium">电话服务</div>
              <div class="f-sm text-center">
                查询服务网点就近送修或现场咨询，
获得检测、维修等服务支持。
              </div>
            </div>

      </section>
    </div>

    <section class="gcal" >
      <div class="text-center text-lg font-bold mb-5">工程案例</div>
      <div class="flex flex-between container" >
        <div>
          <img class="item-img" src="/images/gcal1.jpg" style="" alt="" srcset=""></img>
          <div class="item-content " >
            <div class="text-lg">内蒙古</div>
            <div class="f-min">
              地理位置：内蒙古拉
              <br/>
              施工设备：芳草捆捡拾机
            </div>
          </div>
        </div>
                <div>
          <img class="item-img" src="/images/gcal1.jpg" alt="" srcset=""></img>
           <div class="item-content " >
            <div class="text-lg">内蒙古</div>
            <div class="f-min">
              地理位置：内蒙古拉
              <br/>
              施工设备：芳草捆捡拾机
            </div>
          </div>
        </div>
          <div>
          <img class="item-img" src="/images/gcal1.jpg" alt="" srcset=""></img>
           <div class="item-content " >
            <div class="text-lg">内蒙古</div>
            <div class="f-min">
              地理位置：内蒙古拉
              <br/>
              施工设备：芳草捆捡拾机
            </div>
          </div>
        </div>
      </div>
    </section>

    <div class="banner-container mt-20" style="background-color: #777;">
      
      <img class="banner-img" src="/images/b21.jpg" style="mix-blend-mode:multiply;" alt="产品中心横幅" />

        <div class="banner-content" style="left: 0; width: 100%;text-align: center;">
      <div class="container"> 
          <div style="font-size: 2rem;margin-bottom: 1rem;">技术支持</div>
          <div style="line-height: 2;letter-spacing: 2px;" >秉承“直通到田间，我在你身边”的服务理念，你身边”的服务你身边”的服务你身边”的服务你身边”的服务坚持“响应快、到达快、处置快”的原则，依托分布在全国各地的服务网点，物联网平台信息技术手段，实现人机互联、远程诊断、一键报修等服务，为客户保驾护航。</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from "vue-router"

const router = useRouter()
</script>

<style scoped lang="scss">
.banner {
  position: relative;
  width: 100%;
  height: 320px;
  background: #eee;
  overflow: hidden;
}
.banner-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.banner-content {
  position: absolute;
  left: 35%;
  top: 30%;
  color: #fff;
  text-shadow: 0 2px 8px rgba(0,0,0,0.4);
}
.banner-content h1 {
  font-size: 2.5rem;
  font-style: italic;
  text-align: center;

}
.banner-desc {
  font-size: 1rem;
  margin-top: 1em;
  width: 600px;
  text-align: center;
}
.main-content {
  background-color: #fff;
  padding: 70px;
}

@media (max-width: 900px) {
  .main-content {
    gap: 16px;
  }

}

.gcal{
  .item-img{
    width: 410;height: 280px;
  }
  .item-content{
    background-color: var(--primary-color);
    display: flex;
    gap: 30px;
    padding:6px 30px;
    color: #fff;
    align-items: center;
  }
}
.supper-item{
  cursor: pointer;
  &:hover{
    color: var(--primary-color);
  }
}
</style> 