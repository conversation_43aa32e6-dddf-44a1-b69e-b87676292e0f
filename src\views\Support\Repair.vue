<template>
    <div class="product-center">
      <!-- 顶部横幅 -->
      <div class="banner">
        <img class="banner-img" src="/images/b4.jpg" alt="产品中心横幅" />
        <div class="banner-content">
          <h1>维护保养</h1>
          <div class="banner-desc">紫竹农装  绿色出行</div>
        </div>
      </div>

    <div class="container">
    <div class="main-m" style="width:800px">
      <div class="active">使用手册</div>
      <div>维修教程</div>
      <div>保养教程</div>
    </div>
  </div>

      <div class="container">
        <div>

          
        </div>



                  <!-- 分页功能示例（仅UI，无实际功能） -->
        <div class="paginer">
          <button>上一页</button>
          <button class="active">1</button>
          <button>2</button>
          <button>3</button>
          <span>...</span>
          <button>10</button>
          <button>下一页</button>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref } from 'vue'
  const currentCategory = ref(0);
  const currentProdIdx = ref(0)
  const data = [
    {
        category:'使用手删',
        children:[
            {
                name:'打捆机',
                items:[
                    {name:'9YFQ-2.23方草捆打捆机'},
                    {name:'9YFW-2.2秸秆饲料打捆机'},
                    {name:'9YG-2.25圆捆机'}
                ]
            },
            {
                name:'集捆机',
                items:[
                    {name:'方草捆集捆机'},
                ]
            },
            {
                name:'集垛机',
                items:[
                    {name:'方草捆集捆机'},
                ]
            }
        ]
    },
    {
        category:"维修教程",
        children:[
        {
                name:"综合故障问题",
                items:[
                    {name:'转向失效'},
                    {name:'变速箱、后桥总成损坏'},
                    {name:'制动失效'},
                    {name:'捣缸、冲缸、飞轮炸裂'},
                    {name:'变速箱内的轴断'},
                    {name:'拖拉机作业中断、停驶或性能下降'},
                    {name:'金属表面锈蚀、零件表面强度降低'}
                ]
            },
            {
                name:"油压系统故障",
                items:[
                    {name:'油泵打不起油或输油量过低及压力不足 '},
                    {name:'泵杆与泵杆、 泵杆与衬套之间的间隙过大， 磨损严重 '},
                    {name:'油泵打不起油或输油量过低及压力不足 '},
                    {name:' 回油箱内透气性太差， 油中混气过多 '},
                    {name:'油液粘度太大或油温太高 '},
                    {name:'油泵轴向间隙或径向间隙过大 '},
                    {name:'延时减载阀不能关闭或安全阀开启 '}
                ]
            },
            {
                name:"机电控制系统故障",
                items:[
                    {name:'路老化短路、控制系统电器部件过载'},
                    {name:'机械振动明显'},
                    {name:'自身散热性能不稳定'},
                    {name:'环路接口接触不良'},
                ]
            },

        ]
    },
    {
        category:"保养教程",
        children:[
            {
                name:"",
                items:[
                    {name:'预防沙、尘等物进入机械的摩擦表面'},
                    {name:'经常保持空气、燃油、机油等过滤装置的良好过滤性能'},
                    {name:'清除粘附在机械外部的泥土杂物，减少磨损的因素'},
                    {name:'及时检查、调整各主要机构'},
                    {name:'要向修理点索要维修凭证和所更换零件的发票'},
                    {name:'合乎规格的燃油和润滑油'}
                ]
            },
        ]
    }
  ]

  const d = ref(data);
  
  </script>
  
  <style scoped>
  .product-center {
    background: #f5f5f5;
    min-height: 100vh;
  }

  .main-content {
    display: flex;
    gap: 32px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    padding: 32px;
  }
  
  .product-list {
    flex: 1;
    min-width: 0;

  }
  .product-group {
    margin-bottom: 32px;
  }
  .group-title {
    background: #eaf7db;
    color: #fff;
    font-weight: bold;
    padding: 12px 24px;
    border-radius: 6px 6px 0 0;
    font-size: 1.1rem;
    background: linear-gradient(90deg,var(--primary-color), transparent);
  }
  .group-products {
    background: #fff;
    border-radius: 0 0 6px 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.03);
    padding: 16px 0;
    
    min-height: 600px;
  }
  .item-card {
    display: flex;
    align-items: center;
    gap: 24px;
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
    &:hover{
        color: var(--primary-color);
    }
  }
  .item-card:last-child {
    border-bottom: none;
  }
  .product-img {
    width: 120px;
    height: 90px;
    object-fit: contain;
    border-radius: 8px;
  }
  .product-info {
    flex: 1;
    position: relative;
    padding-left: 20px;
    justify-content: space-between;
    display: flex;

  }
  .product-name {
    font-size: 1rem;
    margin-bottom: 0.5em;
  }

  
  </style> 