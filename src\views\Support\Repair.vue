<template>
    <div class="product-center">
      <!-- 顶部横幅 -->
      <div class="banner">
        <img class="banner-img" src="/images/b4.jpg" alt="产品中心横幅" />
        <div class="banner-content">
          <h1>维护保养</h1>
          <div class="banner-desc">紫竹农装  绿色出行</div>
        </div>
      </div>

    <div class="container">
    <div class="main-m" style="width:800px">
      <div
        :class="{ active: currentCategory === 0 }"
        @click="currentCategory = 0"
      >使用手册</div>
      <div
        :class="{ active: currentCategory === 1 }"
        @click="currentCategory = 1"
      >维修教程</div>
      <div
        :class="{ active: currentCategory === 2 }"
        @click="currentCategory = 2"
      >保养教程</div>
    </div>
  </div>

      <div class="container" style="margin-top: 48px;margin-bottom: 48px">
        <!-- 内容网格区域 -->
        <div class="content-grid">
          <div
            v-for="(item, index) in currentData"
            :key="index"
            class="grid-item"
          >
            <div class="grid-image">
              <img :src="item.img" :alt="item.title" />
            </div>
            <div class="grid-content">
              <h3 class="grid-title">{{ item.title }}</h3>
              <p class="grid-description">{{ item.description }}</p>
              <div class="grid-arrow">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </div>
          </div>
        </div>


      </div>
              <!-- 分页功能示例（仅UI，无实际功能） -->
        <div class="paginer m-20">
          <button>上一页</button>
          <button class="active">1</button>
          <button>2</button>
          <button>3</button>
          <span>...</span>
          <button>10</button>
          <button>下一页</button>
        </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, computed } from 'vue'
  import productsData from '../../assets/products'

  const currentCategory = ref(1);

  // 网格显示数据 - 基于产品数据创建网格项
  const gridData = [
    // 使用手册 - 显示产品
    productsData.map(category => ({
      title: `${category.name}`,
      description: `桥链轮张紧度调整过桥系统：${category.name}`,
      img: category.img,
      category: '使用手册'
    })).flat(),

    // 维修教程 - 显示故障类型
    [
      {
        title: '过桥系统：过桥链轮张紧度调整',
        description: '过桥系统：过桥链轮张紧度调整过桥系统：过桥链轮张紧度调整',
        img: '/images/qa1.jpg',
        category: '维修教程'
      },
      {
        title: '过桥系统：过桥链轮张紧度调整',
        description: '过桥系统：过桥链轮张紧度调整过桥系统：过桥链轮张紧度调整',
        img: '/images/qa2.jpg',
        category: '维修教程'
      },
      {
        title: '过桥系统：过桥链轮张紧度调整',
        description: '过桥系统：过桥链轮张紧度调整过桥系统：过桥链轮张紧度调整',
        img: '/images/qa3.jpg',
        category: '维修教程'
      },
      {
        title: '过桥系统：过桥链轮张紧度调整',
        description: '过桥系统：过桥链轮张紧度调整过桥系统：过桥链轮张紧度调整',
        img: '/images/qa2.jpg',
        category: '维修教程'
      },
      {
        title: '过桥系统：过桥链轮张紧度调整',
        description: '过桥系统：过桥链轮张紧度调整过桥系统：过桥链轮张紧度调整',
        img: '/images/qa3.jpg',
        category: '维修教程'
      },
      {
        title: '过桥系统：过桥链轮张紧度调整',
        description: '过桥系统：过桥链轮张紧度调整过桥系统：过桥链轮张紧度调整',
        img: '/images/qa1.jpg',
        category: '维修教程'
      }
    ],

    // 保养教程 - 显示保养项目
    [
      {
        title: '过桥系统：过桥链轮张紧度调整',
        description: '过桥系统：过桥链轮张紧度调整过桥系统：过桥链轮张紧度调整',
        img: '/images/qa1.jpg',
        category: '保养教程'
      },
      {
        title: '过桥系统：过桥链轮张紧度调整',
        description: '过桥系统：过桥链轮张紧度调整过桥系统：过桥链轮张紧度调整',
        img: '/images/qa3.jpg',
        category: '保养教程'
      },
      {
        title: '过桥系统：过桥链轮张紧度调整',
        description: '过桥系统：过桥链轮张紧度调整过桥系统：过桥链轮张紧度调整',
        img: '/images/qa2.jpg',
        category: '保养教程'
      },
      {
        title: '过桥系统：过桥链轮张紧度调整',
        description: '过桥系统：过桥链轮张紧度调整过桥系统：过桥链轮张紧度调整',
        img: '/images/qa2.jpg',
        category: '保养教程'
      },
      {
        title: '过桥系统：过桥链轮张紧度调整',
        description: '过桥系统：过桥链轮张紧度调整过桥系统：过桥链轮张紧度调整',
        img: '/images/qa1.jpg',
        category: '保养教程'
      },
      {
        title: '过桥系统：过桥链轮张紧度调整',
        description: '过桥系统：过桥链轮张紧度调整过桥系统：过桥链轮张紧度调整',
        img: '/images/qa3.jpg',
        category: '保养教程'
      }
    ]
  ]

  // 当前显示的数据
  const currentData = computed(() => {
    return gridData[currentCategory.value] || []
  })
  
  </script>
  
  <style scoped>
  .product-center {
    background: #f5f5f5;
    min-height: 100vh;
  }

  .main-content {
    display: flex;
    gap: 32px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    padding: 32px;
  }
  
  .product-list {
    flex: 1;
    min-width: 0;

  }
  .product-group {
    margin-bottom: 32px;
  }
  .group-title {
    background: #eaf7db;
    color: #fff;
    font-weight: bold;
    padding: 12px 24px;
    border-radius: 6px 6px 0 0;
    font-size: 1.1rem;
    background: linear-gradient(90deg,var(--primary-color), transparent);
  }
  .group-products {
    background: #fff;
    border-radius: 0 0 6px 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.03);
    padding: 16px 0;
    
    min-height: 600px;
  }
  .item-card {
    display: flex;
    align-items: center;
    gap: 24px;
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
    &:hover{
        color: var(--primary-color);
    }
  }
  .item-card:last-child {
    border-bottom: none;
  }
  .product-img {
    width: 120px;
    height: 90px;
    object-fit: contain;
    border-radius: 8px;
  }
  .product-info {
    flex: 1;
    position: relative;
    padding-left: 20px;
    justify-content: space-between;
    display: flex;

  }
  .product-name {
    font-size: 1rem;
    margin-bottom: 0.5em;
  }

  /* 网格布局样式 */
  .content-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin: 40px 0;
    padding: 0 20px;
  }

  .grid-item {
    background: #fff;
    /* border-radius: 8px; */
    overflow: hidden;
    /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
  }

  .grid-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  .grid-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
  }

  .grid-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .grid-item:hover .grid-image img {
    transform: scale(1.05);
  }

  .grid-content {
    padding: 20px;
    position: relative;
  }

  .grid-title {
    font-size: 1.1rem;
    font-weight: bold;
    color: #333;
    margin: 0 0 12px 0;
    line-height: 1.4;
  }

  .grid-description {
    font-size: 0.9rem;
    color: #666;
    line-height: 1.5;
    margin: 0 0 16px 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .grid-arrow {
    position: absolute;
    bottom: 20px;
    right: 20px;
    color: #999;
    transition: color 0.3s ease, transform 0.3s ease;
  }

  .grid-item:hover .grid-arrow {
    color: #333;
    transform: translateX(4px);
  }

  /* main-m 菜单样式 */


  .main-m > div:hover {
    background: #f8f8f8;
    color: #333;
  }

  .main-m > div.active {
    background: var(--primary-color, #4CAF50);
    color: #fff;
  }

  /* 响应式设计 */
  @media (max-width: 1024px) {
    .content-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
    }
  }

  @media (max-width: 768px) {
    .content-grid {
      grid-template-columns: 1fr;
      gap: 16px;
      padding: 0 16px;
    }

    .main-m {
      margin: 20px 16px;
    }

    .main-m > div {
      padding: 12px 16px;
      font-size: 0.9rem;
    }
  }

  </style>